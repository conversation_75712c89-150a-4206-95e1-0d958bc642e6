"""
Simple test to verify basic rendering is working again.
Run this in Blender's Text Editor.
"""

import bpy

def test_basic_multiple_items():
    """Test that basic multiple items work"""
    print("🧪 Testing Basic Multiple Items...")
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ SDF tree not found")
        return False
    
    tree = scene.sdf_tree
    
    # Clear tree
    tree.items.clear()
    tree.active_index = 0
    
    # Add two simple primitives
    bpy.ops.sdf.tree_add_sphere()
    sphere = tree.items[0]
    sphere.name = "Test Sphere"
    sphere.location = (-1.0, 0.0, 0.0)
    sphere.radius = 0.8
    
    bpy.ops.sdf.tree_add_box()
    box = tree.items[1]
    box.name = "Test Box"
    box.location = (1.0, 0.0, 0.0)
    box.size = (0.8, 0.8, 0.8)
    box.boolean_mode = 'UNION'
    
    print(f"✅ Added {len(tree.items)} items")
    
    # Test GLSL generation
    try:
        glsl = tree.generate_glsl()
        print(f"Generated GLSL:\n{glsl}")
        
        # Check for basic structure
        if "return" in glsl:
            print("✅ GLSL has return statement")
        else:
            print("❌ GLSL missing return statement")
            return False
        
        # Check for multiple items
        if "result" in glsl and "d1" in glsl:
            print("✅ GLSL processes multiple items")
        else:
            print("❌ GLSL doesn't seem to process multiple items")
            return False
        
        # Check for boolean operations
        if "min(" in glsl:
            print("✅ GLSL has boolean operations")
        else:
            print("❌ GLSL missing boolean operations")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_single_item():
    """Test single item rendering"""
    print("\n🧪 Testing Single Item...")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Clear tree
    tree.items.clear()
    tree.active_index = 0
    
    # Add single item
    bpy.ops.sdf.tree_add_sphere()
    sphere = tree.items[0]
    sphere.name = "Single Sphere"
    sphere.radius = 1.5
    
    try:
        glsl = tree.generate_glsl()
        print(f"Single item GLSL:\n{glsl}")
        
        if "return" in glsl and "length(p" in glsl:
            print("✅ Single item GLSL correct")
            return True
        else:
            print("❌ Single item GLSL incorrect")
            return False
            
    except Exception as e:
        print(f"❌ Single item GLSL failed: {e}")
        return False

def test_three_items():
    """Test three items to make sure all are processed"""
    print("\n🧪 Testing Three Items...")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Clear tree
    tree.items.clear()
    tree.active_index = 0
    
    # Add three items
    bpy.ops.sdf.tree_add_sphere()
    sphere = tree.items[0]
    sphere.name = "Sphere 1"
    sphere.location = (-2.0, 0.0, 0.0)
    
    bpy.ops.sdf.tree_add_box()
    box = tree.items[1]
    box.name = "Box 1"
    box.location = (0.0, 0.0, 0.0)
    box.boolean_mode = 'UNION'
    
    bpy.ops.sdf.tree_add_cylinder()
    cylinder = tree.items[2]
    cylinder.name = "Cylinder 1"
    cylinder.location = (2.0, 0.0, 0.0)
    cylinder.boolean_mode = 'SUBTRACT'
    
    try:
        glsl = tree.generate_glsl()
        print(f"Three items GLSL:\n{glsl}")
        
        # Should have d1 and d2 for the second and third items
        if "d1" in glsl and "d2" in glsl:
            print("✅ All three items processed")
            return True
        else:
            print("❌ Not all three items processed")
            print(f"Has d1: {'d1' in glsl}, Has d2: {'d2' in glsl}")
            return False
            
    except Exception as e:
        print(f"❌ Three items GLSL failed: {e}")
        return False

def run_basic_tests():
    """Run all basic rendering tests"""
    print("🚀 Starting Basic Rendering Tests")
    print("=" * 50)
    
    tests = [
        test_single_item,
        test_basic_multiple_items,
        test_three_items,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ Test passed\n")
            else:
                print("❌ Test failed\n")
        except Exception as e:
            print(f"❌ Test error: {e}\n")
    
    print("=" * 50)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic rendering tests passed!")
        print("Multiple items should now render correctly in the viewport!")
        return True
    else:
        print("⚠️  Some tests failed.")
        return False

if __name__ == "__main__":
    run_basic_tests()
