"""
Debug script to diagnose matcap rendering issues.
Run this to see what's happening with matcap rendering.
"""

import bpy


def debug_matcap_state():
    """Debug the current matcap state"""
    print("\n=== DEBUGGING MATCAP STATE ===")
    
    try:
        from .shaders import SDFRenderer
        
        # Check renderer state
        print(f"Renderer enabled: {SDFRenderer.is_enabled()}")
        print(f"Has shader: {SDFRenderer._shader is not None}")
        print(f"Has matcap texture: {SDFRenderer._matcap_texture is not None}")
        
        if SDFRenderer._matcap_texture:
            print(f"Matcap texture type: {type(SDFRenderer._matcap_texture)}")
        
        # Check scene properties
        scene = bpy.context.scene
        if hasattr(scene, 'sdf'):
            print(f"Viewport rendering enabled: {scene.sdf.sdf_show_in_viewport}")
            
            if hasattr(scene.sdf, 'material'):
                mat = scene.sdf.material
                print(f"Use matcap: {mat.use_matcap}")
                print(f"Shading mode: {mat.shading_mode}")
                print(f"Matcap intensity: {mat.matcap_intensity}")
                print(f"Base color: {mat.base_color}")
                print(f"Matcap image: {mat.matcap_image}")
                
                if hasattr(mat, 'matcap_studio_light'):
                    print(f"Matcap studio light: {mat.matcap_studio_light}")
        
        # Check if there are any loaded images that could be matcaps
        print("\nLoaded images:")
        for image_name, image in bpy.data.images.items():
            if image_name.lower().endswith('.exr') or 'matcap' in image_name.lower():
                print(f"  {image_name}: {image.size}")
        
        return True
        
    except Exception as e:
        print(f"Error debugging matcap state: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_manual_matcap_setup():
    """Manually set up matcap to see if it works"""
    print("\n=== TESTING MANUAL MATCAP SETUP ===")
    
    try:
        scene = bpy.context.scene
        
        # Ensure viewport rendering is enabled
        scene.sdf.sdf_show_in_viewport = True
        print("✅ Enabled viewport rendering")
        
        # Set up material properties
        mat = scene.sdf.material
        mat.use_matcap = True
        mat.shading_mode = '1'  # Matcap mode
        mat.matcap_intensity = 1.0
        mat.base_color = (1.0, 1.0, 1.0, 1.0)  # White base
        print("✅ Set material properties for matcap")
        
        # Try to load default matcap
        from .shaders import SDFRenderer
        if SDFRenderer.create_default_matcap():
            print("✅ Created default matcap")
        else:
            print("❌ Failed to create default matcap")
            return False
        
        # Force shader refresh
        SDFRenderer.refresh_shader()
        print("✅ Refreshed shader")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in manual matcap setup: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_exr_matcap_loading():
    """Test loading an EXR file as matcap"""
    print("\n=== TESTING EXR MATCAP LOADING ===")
    
    try:
        # Find any EXR files in loaded images
        exr_images = []
        for image_name, image in bpy.data.images.items():
            if image_name.lower().endswith('.exr'):
                exr_images.append(image_name)
        
        if not exr_images:
            print("❌ No EXR images found in Blender")
            return False
        
        print(f"Found EXR images: {exr_images}")
        
        # Try to load the first EXR as matcap
        exr_name = exr_images[0]
        print(f"Testing with: {exr_name}")
        
        from .shaders import SDFRenderer
        
        # Load the EXR as matcap
        if SDFRenderer.load_matcap_from_blender_image(exr_name):
            print(f"✅ Loaded {exr_name} as matcap texture")
            
            # Set material properties to use it
            scene = bpy.context.scene
            mat = scene.sdf.material
            mat.matcap_image = exr_name
            mat.use_matcap = True
            mat.shading_mode = '1'  # Matcap mode
            mat.matcap_intensity = 1.0
            
            print("✅ Set material properties to use EXR matcap")
            
            # Force shader refresh
            SDFRenderer.refresh_shader()
            print("✅ Refreshed shader")
            
            return True
        else:
            print(f"❌ Failed to load {exr_name} as matcap")
            return False
        
    except Exception as e:
        print(f"❌ Error testing EXR matcap loading: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_shader_uniforms():
    """Test if shader uniforms are being set correctly"""
    print("\n=== TESTING SHADER UNIFORMS ===")
    
    try:
        from .shaders import SDFRenderer
        
        if not SDFRenderer._shader:
            print("❌ No shader available")
            return False
        
        scene = bpy.context.scene
        
        print("Testing uniform setting...")
        SDFRenderer._set_matcap_uniforms(scene)
        print("✅ Uniforms set without error")
        
        # Check if material properties are being read correctly
        if hasattr(scene.sdf, 'material'):
            mat = scene.sdf.material
            print(f"Material use_matcap: {mat.use_matcap}")
            print(f"Material shading_mode: {mat.shading_mode}")
            print(f"Material matcap_intensity: {mat.matcap_intensity}")
            
            # Convert shading mode to int like the shader does
            shading_mode_int = int(mat.shading_mode)
            print(f"Shading mode as int: {shading_mode_int}")
            
            if mat.use_matcap and shading_mode_int in [1, 2]:
                print("✅ Material is configured for matcap rendering")
            else:
                print("❌ Material is NOT configured for matcap rendering")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing shader uniforms: {e}")
        import traceback
        traceback.print_exc()
        return False


def force_matcap_debug():
    """Force enable matcap with debug output"""
    print("\n=== FORCING MATCAP DEBUG ===")
    
    try:
        scene = bpy.context.scene
        
        # Force enable everything
        scene.sdf.sdf_show_in_viewport = True
        mat = scene.sdf.material
        mat.use_matcap = True
        mat.shading_mode = '1'
        mat.matcap_intensity = 1.0
        mat.base_color = (1.0, 0.0, 0.0, 1.0)  # Red base to see if it's working
        
        print("✅ Forced material settings")
        
        # Create or load matcap
        from .shaders import SDFRenderer
        
        # Try to use any available image first
        available_images = list(bpy.data.images.keys())
        print(f"Available images: {available_images}")
        
        matcap_loaded = False
        
        # Try to load from any EXR file
        for img_name in available_images:
            if img_name.lower().endswith('.exr'):
                print(f"Trying to load {img_name} as matcap...")
                if SDFRenderer.load_matcap_from_blender_image(img_name):
                    mat.matcap_image = img_name
                    matcap_loaded = True
                    print(f"✅ Loaded {img_name} as matcap")
                    break
        
        # Fallback to default matcap
        if not matcap_loaded:
            print("Creating default matcap...")
            if SDFRenderer.create_default_matcap():
                mat.matcap_image = "SDF_Default_Matcap"
                matcap_loaded = True
                print("✅ Created default matcap")
        
        if not matcap_loaded:
            print("❌ No matcap could be loaded")
            return False
        
        # Force shader refresh
        print("Forcing shader refresh...")
        SDFRenderer.refresh_shader()
        
        print("✅ Matcap debug setup complete")
        print("Check the viewport - you should see red-tinted matcap rendering")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in force matcap debug: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_matcap_debug():
    """Run all matcap debugging tests"""
    print("=" * 60)
    print("MATCAP RENDERING DEBUG")
    print("=" * 60)
    
    tests = [
        ("Debug Matcap State", debug_matcap_state),
        ("Manual Matcap Setup", test_manual_matcap_setup),
        ("EXR Matcap Loading", test_exr_matcap_loading),
        ("Shader Uniforms", test_shader_uniforms),
        ("Force Matcap Debug", force_matcap_debug),
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
    
    print("\n" + "=" * 60)
    print("DEBUG COMPLETE")
    print("=" * 60)
    print("If matcap still doesn't show, the issue might be:")
    print("1. Shader compilation problem")
    print("2. Texture format incompatibility")
    print("3. OpenGL state issues")
    print("4. View matrix calculation problems")


if __name__ == "__main__":
    run_matcap_debug()
