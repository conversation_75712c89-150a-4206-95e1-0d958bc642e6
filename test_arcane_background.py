"""
Test script for arcane.png panel backgrounds.
This script tests the simplified background system.
"""

import bpy
import os


def create_arcane_placeholder():
    """Create a placeholder arcane.png image for testing"""
    print("\n=== Creating Arcane Placeholder ===")
    
    try:
        # Create a placeholder arcane.png image
        image_name = "arcane.png"
        
        # Remove existing image if it exists
        if image_name in bpy.data.images:
            bpy.data.images.remove(bpy.data.images[image_name])
        
        # Create new image with arcane-style design
        image = bpy.data.images.new(image_name, 1024, 1024, alpha=True)
        
        # Create an arcane-style pattern
        pixels = []
        for y in range(1024):
            for x in range(1024):
                # Create mystical pattern
                center_x, center_y = 512, 512
                distance = ((x - center_x) ** 2 + (y - center_y) ** 2) ** 0.5
                
                # Radial pattern
                angle = (x - center_x) / max(1, distance) * 10
                radial = (distance / 512) * 3.14159
                
                # Create mystical colors (purple/blue/gold)
                r = 0.2 + 0.3 * abs(angle % 1.0 - 0.5)
                g = 0.1 + 0.2 * abs((radial * 2) % 1.0 - 0.5)
                b = 0.6 + 0.4 * abs((angle + radial) % 1.0 - 0.5)
                
                # Add some golden highlights
                if (x + y) % 100 < 5:
                    r += 0.4
                    g += 0.3
                
                # Add mystical circles
                if abs(distance - 200) < 3 or abs(distance - 350) < 2:
                    r, g, b = 0.8, 0.6, 0.2  # Golden circles
                
                # Ensure values are in range
                r = min(1.0, max(0.0, r))
                g = min(1.0, max(0.0, g))
                b = min(1.0, max(0.0, b))
                
                pixels.extend([r, g, b, 0.8])  # Semi-transparent
        
        # Set image pixels
        image.pixels = pixels
        image.pack()
        
        print(f"✅ Created placeholder arcane.png: {image_name}")
        return image_name
        
    except Exception as e:
        print(f"❌ Error creating placeholder arcane.png: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_sdf_panel_background():
    """Test the SDF panel background system"""
    print("\n=== Testing SDF Panel Background ===")
    
    try:
        from .panel_backgrounds import SDFPanelBackground
        
        # Initialize the system
        if SDFPanelBackground.initialize():
            print("✅ SDF Panel Background initialized")
        else:
            print("❌ Failed to initialize SDF Panel Background")
            return False
        
        # Check if arcane.png was loaded
        if SDFPanelBackground._texture:
            print("✅ Arcane texture loaded successfully")
        else:
            print("⚠️  No arcane texture loaded, creating placeholder...")
            # Create placeholder and try again
            create_arcane_placeholder()
            if SDFPanelBackground._load_arcane_texture():
                print("✅ Placeholder arcane texture loaded")
            else:
                print("❌ Failed to load arcane texture")
                return False
        
        print("✅ SDF Panel Background test completed")
        return True
        
    except Exception as e:
        print(f"❌ Error testing SDF Panel Background: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_panel_drawing():
    """Test panel drawing with background"""
    print("\n=== Testing Panel Drawing ===")
    
    try:
        from .panel_backgrounds import SDFPanelMixin
        
        # Create a mock context for testing
        print("✅ SDFPanelMixin class available")
        
        # The actual drawing test requires a UI context
        print("✅ Panel drawing test completed (requires UI context for full test)")
        return True
        
    except Exception as e:
        print(f"❌ Error testing panel drawing: {e}")
        import traceback
        traceback.print_exc()
        return False


def verify_panel_integration():
    """Verify that panels are properly integrated"""
    print("\n=== Verifying Panel Integration ===")
    
    try:
        # Check if the panels exist and have the mixin
        from . import tree_panels
        
        # Check main panel
        main_panel = tree_panels.SDF_PT_TreePanel
        if hasattr(main_panel, 'draw_arcane_background'):
            print("✅ Main panel has arcane background support")
        else:
            print("❌ Main panel missing arcane background support")
            return False
        
        # Check settings panel
        settings_panel = tree_panels.SDF_PT_TreeSettings
        if hasattr(settings_panel, 'draw_arcane_background'):
            print("✅ Settings panel has arcane background support")
        else:
            print("❌ Settings panel missing arcane background support")
            return False
        
        print("✅ Panel integration verified")
        return True
        
    except Exception as e:
        print(f"❌ Error verifying panel integration: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_arcane_file():
    """Check if arcane.png exists in the addon directory"""
    print("\n=== Checking for arcane.png File ===")
    
    try:
        addon_dir = os.path.dirname(__file__)
        arcane_path = os.path.join(addon_dir, "arcane.png")
        
        if os.path.exists(arcane_path):
            print(f"✅ Found arcane.png at: {arcane_path}")
            
            # Try to load it in Blender
            if "arcane.png" not in bpy.data.images:
                image = bpy.data.images.load(arcane_path)
                image.name = "arcane.png"
                print("✅ Loaded arcane.png into Blender")
            else:
                print("✅ arcane.png already loaded in Blender")
            
            return True
        else:
            print(f"⚠️  arcane.png not found at: {arcane_path}")
            print("Creating placeholder for testing...")
            create_arcane_placeholder()
            return True
        
    except Exception as e:
        print(f"❌ Error checking arcane.png file: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_arcane_background_tests():
    """Run all arcane background tests"""
    print("=" * 60)
    print("ARCANE PANEL BACKGROUND TESTING")
    print("=" * 60)
    
    tests = [
        ("Check arcane.png File", check_arcane_file),
        ("SDF Panel Background System", test_sdf_panel_background),
        ("Panel Drawing", test_panel_drawing),
        ("Panel Integration", verify_panel_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("ARCANE BACKGROUND TEST RESULTS")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
    
    print(f"\nPassed: {passed}/{total} tests")
    
    if passed == total:
        print("🎉 All arcane background tests passed!")
        print("\nYour SDF panels should now have arcane.png as background at 25% transparency!")
        print("Check the 'Arcane SDF' panels in the 3D viewport sidebar.")
    else:
        print("⚠️  Some tests failed. Check the output above.")
    
    return passed == total


if __name__ == "__main__":
    run_arcane_background_tests()
