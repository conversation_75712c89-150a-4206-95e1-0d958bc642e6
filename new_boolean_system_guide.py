#!/usr/bin/env python3
"""
Guide for the NEW Boolean System in Arcane SDF.
Now boolean operations are properties of primitives, not separate items!

Run this from within Blender's Python console to see examples.
"""

import bpy

def clear_tree():
    """Clear the SDF tree and start fresh"""
    scene = bpy.context.scene
    if hasattr(scene, 'sdf_tree'):
        tree = scene.sdf_tree
        tree.items.clear()
        tree.active_index = 0
        print("Tree cleared")

def example_union():
    """Example: Create a union using boolean modes"""
    print("\n=== NEW UNION EXAMPLE ===")
    print("Creating a dumbbell shape using Union mode...")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Add first sphere (automatically Union mode)
    bpy.ops.sdf.tree_add_sphere()
    sphere1 = tree.items[-1]
    sphere1.name = "Left Sphere"
    sphere1.radius = 0.8
    sphere1.location = (-1.2, 0.0, 0.0)
    sphere1.boolean_mode = 'UNION'  # Default, but showing explicitly
    
    # Add second sphere (Union mode)
    bpy.ops.sdf.tree_add_sphere()
    sphere2 = tree.items[-1]
    sphere2.name = "Right Sphere"
    sphere2.radius = 0.8
    sphere2.location = (1.2, 0.0, 0.0)
    sphere2.boolean_mode = 'UNION'
    
    # Add connecting cylinder (Union mode with smooth blending)
    bpy.ops.sdf.tree_add_cylinder()
    cylinder = tree.items[-1]
    cylinder.name = "Connector"
    cylinder.radius = 0.3
    cylinder.height = 2.4
    cylinder.location = (0.0, 0.0, 0.0)
    cylinder.boolean_mode = 'UNION'
    cylinder.smooth_radius = 0.2  # Smooth blending
    
    print("✅ Dumbbell created! All shapes use Union mode with smooth blending.")

def example_subtraction():
    """Example: Create subtraction using boolean modes"""
    print("\n=== NEW SUBTRACTION EXAMPLE ===")
    print("Creating a sphere with holes using Subtract mode...")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Add base sphere (Union mode - this is the base)
    bpy.ops.sdf.tree_add_sphere()
    base_sphere = tree.items[-1]
    base_sphere.name = "Base Sphere"
    base_sphere.radius = 1.5
    base_sphere.location = (0.0, 0.0, 0.0)
    base_sphere.boolean_mode = 'UNION'  # First item is always the base
    
    # Add hole cylinder through X axis (Subtract mode)
    bpy.ops.sdf.tree_add_cylinder()
    hole_x = tree.items[-1]
    hole_x.name = "X Hole"
    hole_x.radius = 0.4
    hole_x.height = 4.0
    hole_x.location = (0.0, 0.0, 0.0)
    hole_x.rotation = (0.0, 1.57, 0.0)  # Rotate 90 degrees around Y
    hole_x.boolean_mode = 'SUBTRACT'  # This will cut a hole
    
    # Add hole cylinder through Y axis (Subtract mode)
    bpy.ops.sdf.tree_add_cylinder()
    hole_y = tree.items[-1]
    hole_y.name = "Y Hole"
    hole_y.radius = 0.4
    hole_y.height = 4.0
    hole_y.location = (0.0, 0.0, 0.0)
    hole_y.rotation = (1.57, 0.0, 0.0)  # Rotate 90 degrees around X
    hole_y.boolean_mode = 'SUBTRACT'  # This will cut another hole
    
    # Add hole cylinder through Z axis (Subtract mode)
    bpy.ops.sdf.tree_add_cylinder()
    hole_z = tree.items[-1]
    hole_z.name = "Z Hole"
    hole_z.radius = 0.4
    hole_z.height = 4.0
    hole_z.location = (0.0, 0.0, 0.0)
    hole_z.boolean_mode = 'SUBTRACT'  # This will cut the third hole
    
    print("✅ Sphere with 3 holes created! Base uses Union, holes use Subtract mode.")

def example_intersection():
    """Example: Create intersection using boolean modes"""
    print("\n=== NEW INTERSECTION EXAMPLE ===")
    print("Creating a rounded cube using Intersection mode...")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Add base cube (Union mode)
    bpy.ops.sdf.tree_add_box()
    cube = tree.items[-1]
    cube.name = "Base Cube"
    cube.size = (1.2, 1.2, 1.2)
    cube.location = (0.0, 0.0, 0.0)
    cube.boolean_mode = 'UNION'
    
    # Add sphere to round the corners (Intersect mode)
    bpy.ops.sdf.tree_add_sphere()
    sphere = tree.items[-1]
    sphere.name = "Rounding Sphere"
    sphere.radius = 1.5
    sphere.location = (0.0, 0.0, 0.0)
    sphere.boolean_mode = 'INTERSECT'  # Keep only the intersection
    sphere.smooth_radius = 0.1  # Smooth the intersection
    
    print("✅ Rounded cube created! Cube uses Union, sphere uses Intersect mode.")

def example_complex_shape():
    """Example: Create a complex shape mixing all boolean modes"""
    print("\n=== COMPLEX SHAPE EXAMPLE ===")
    print("Creating a complex shape using all boolean modes...")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Base: Large sphere
    bpy.ops.sdf.tree_add_sphere()
    base = tree.items[-1]
    base.name = "Base"
    base.radius = 1.5
    base.boolean_mode = 'UNION'
    
    # Add: Small spheres on the sides
    bpy.ops.sdf.tree_add_sphere()
    add1 = tree.items[-1]
    add1.name = "Left Addition"
    add1.radius = 0.6
    add1.location = (-1.8, 0.0, 0.0)
    add1.boolean_mode = 'UNION'
    add1.smooth_radius = 0.3
    
    bpy.ops.sdf.tree_add_sphere()
    add2 = tree.items[-1]
    add2.name = "Right Addition"
    add2.radius = 0.6
    add2.location = (1.8, 0.0, 0.0)
    add2.boolean_mode = 'UNION'
    add2.smooth_radius = 0.3
    
    # Subtract: Central hole
    bpy.ops.sdf.tree_add_cylinder()
    hole = tree.items[-1]
    hole.name = "Central Hole"
    hole.radius = 0.5
    hole.height = 4.0
    hole.boolean_mode = 'SUBTRACT'
    
    # Intersect: Bounding box to create flat top/bottom
    bpy.ops.sdf.tree_add_box()
    bounds = tree.items[-1]
    bounds.name = "Bounding Box"
    bounds.size = (4.0, 4.0, 1.0)
    bounds.boolean_mode = 'INTERSECT'
    bounds.smooth_radius = 0.1
    
    print("✅ Complex shape created using all boolean modes!")

def show_tree_with_boolean_modes():
    """Display the current tree structure with boolean modes"""
    print("\n=== TREE STRUCTURE WITH BOOLEAN MODES ===")
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("No SDF tree found!")
        return
    
    tree = scene.sdf_tree
    print(f"Tree has {len(tree.items)} items:")
    
    for i, item in enumerate(tree.items):
        enabled_info = " [DISABLED]" if not item.is_enabled else ""
        boolean_info = f" [{item.boolean_mode}]" if item.item_type in ['SPHERE', 'BOX', 'CYLINDER', 'TORUS'] else ""
        smooth_info = f" (smooth: {item.smooth_radius})" if item.smooth_radius > 0 else ""
        
        print(f"  {i}: {item.name} ({item.item_type}){boolean_info}{smooth_info}{enabled_info}")

def test_new_boolean_system():
    """Test the new boolean system"""
    print("🚀 Testing NEW Boolean System in Arcane SDF")
    print("=" * 50)
    print("✨ Boolean operations are now properties of primitives!")
    print("✨ No more complex tree hierarchies!")
    print("✨ Much more intuitive workflow!")
    
    examples = [
        ("Union (Dumbbell)", example_union),
        ("Subtraction (Sphere with holes)", example_subtraction),
        ("Intersection (Rounded cube)", example_intersection),
        ("Complex Shape (All modes)", example_complex_shape),
    ]
    
    for name, example_func in examples:
        try:
            example_func()
            show_tree_with_boolean_modes()
            input(f"\nPress Enter to continue to next example...")
        except Exception as e:
            print(f"❌ Error in {name} example: {e}")
    
    print("\n🎉 New boolean system testing complete!")
    print("\n📚 QUICK REFERENCE:")
    print("1. Add primitives using the primitive buttons")
    print("2. Select a primitive in the tree list")
    print("3. Change its 'Boolean Mode' in the properties:")
    print("   • Union: Add this shape to the result")
    print("   • Subtract: Cut this shape from the result")
    print("   • Intersect: Keep only overlapping parts")
    print("4. Adjust 'Smooth Radius' for smooth blending")
    print("5. Shapes are processed in tree order (top to bottom)")

if __name__ == "__main__":
    test_new_boolean_system()
