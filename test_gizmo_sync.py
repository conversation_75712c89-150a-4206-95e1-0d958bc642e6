"""
Test script to verify gizmo synchronization with primitives.
Run this in Blender's Text Editor to test that gizmo stays with the object.
"""

import bpy
from mathutils import Vector

def create_sync_test_scene():
    """Create a test scene for gizmo synchronization"""
    print("🎬 Creating Gizmo Sync Test Scene")
    print("=" * 40)
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ No SDF tree")
        return False
    
    tree = scene.sdf_tree
    
    # Clear and create test items
    tree.items.clear()
    tree.active_index = 0
    
    # Add a sphere at a known position
    bpy.ops.sdf.tree_add_sphere()
    sphere = tree.items[0]
    sphere.name = "Sync Test Sphere"
    sphere.location = (2.0, 1.0, 0.5)  # Non-zero position for better testing
    sphere.radius = 1.0
    
    # Select it
    tree.active_index = 0
    
    print(f"✅ Created test sphere at: {tuple(sphere.location)}")
    print("   This position should match the gizmo location")
    
    return True

def test_gizmo_position_accuracy():
    """Test that gizmo appears at the correct position"""
    print("\n📍 Testing Gizmo Position Accuracy")
    print("=" * 40)
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    if not tree.items:
        print("❌ No items to test")
        return False
    
    item = tree.items[tree.active_index]
    expected_world_pos = item.get_world_location(tree)
    
    print(f"Item location: {tuple(item.location)}")
    print(f"Expected world position: {expected_world_pos}")
    
    # Enable gizmos if not already enabled
    try:
        from .sdf_simple_widgets import _widget_state
        if not _widget_state['enabled']:
            bpy.ops.sdf.toggle_simple_widgets()
        
        if _widget_state['enabled']:
            print("✅ Gizmos enabled")
            print("👀 Check that gizmo axes appear at the expected world position")
            return True
        else:
            print("❌ Could not enable gizmos")
            return False
            
    except Exception as e:
        print(f"❌ Gizmo position test failed: {e}")
        return False

def test_movement_synchronization():
    """Test that gizmo stays synchronized during movement"""
    print("\n🔄 Testing Movement Synchronization")
    print("=" * 40)
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    if not tree.items:
        print("❌ No items to test")
        return False
    
    item = tree.items[tree.active_index]
    
    # Record initial position
    initial_pos = tuple(item.location)
    print(f"Initial position: {initial_pos}")
    
    # Test programmatic movement (like X/Y/Z buttons would do)
    test_movements = [
        (1.0, 0.0, 0.0, "X-axis"),
        (0.0, 1.0, 0.0, "Y-axis"),
        (0.0, 0.0, 1.0, "Z-axis"),
    ]
    
    for dx, dy, dz, axis_name in test_movements:
        # Move the item
        item.location[0] += dx
        item.location[1] += dy
        item.location[2] += dz
        
        new_pos = tuple(item.location)
        world_pos = item.get_world_location(tree)
        
        print(f"After {axis_name} move: {new_pos}")
        print(f"World position: {world_pos}")
        
        # Force viewport redraw (like the buttons do)
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
        
        print(f"✅ {axis_name} movement test complete")
    
    # Reset to initial position
    item.location = initial_pos
    for area in bpy.context.screen.areas:
        if area.type == 'VIEW_3D':
            area.tag_redraw()
    
    print(f"✅ Reset to initial position: {tuple(item.location)}")
    print("👀 Gizmo should have followed all movements and returned to start")
    
    return True

def test_3d_movement_calculation():
    """Test the new 3D movement calculation"""
    print("\n🧮 Testing 3D Movement Calculation")
    print("=" * 40)
    
    try:
        # Import the gizmo operator
        from .sdf_simple_widgets import SDF_OT_GizmoInteraction
        
        # Create test instance
        gizmo_op = SDF_OT_GizmoInteraction()
        
        # Test if the method exists
        if hasattr(gizmo_op, 'calculate_3d_movement'):
            print("✅ 3D movement calculation method found")
            
            # We can't easily test the actual calculation without a real mouse event
            # But we can verify the method is callable
            print("✅ Method is accessible for mouse interaction")
            return True
        else:
            print("❌ 3D movement calculation method not found")
            return False
            
    except Exception as e:
        print(f"❌ 3D movement test failed: {e}")
        return False

def test_hierarchical_sync():
    """Test gizmo sync with hierarchical transforms"""
    print("\n🌳 Testing Hierarchical Sync")
    print("=" * 40)
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Clear and create hierarchy
    tree.items.clear()
    tree.active_index = 0
    
    # Create parent group
    bpy.ops.sdf.tree_add_group()
    parent = tree.items[0]
    parent.name = "Parent Group"
    parent.location = (1.0, 0.0, 0.0)
    
    # Add child sphere
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='SPHERE')
    child = tree.items[1]
    child.name = "Child Sphere"
    child.location = (2.0, 0.0, 0.0)  # Local offset
    child.radius = 0.8
    
    # Test child selection and world position
    tree.active_index = 1  # Select child
    child_world_pos = child.get_world_location(tree)
    expected_world_pos = (3.0, 0.0, 0.0)  # Parent (1,0,0) + Child (2,0,0)
    
    print(f"Parent location: {tuple(parent.location)}")
    print(f"Child local location: {tuple(child.location)}")
    print(f"Child world location: {child_world_pos}")
    print(f"Expected world location: {expected_world_pos}")
    
    # Check if world position is correct
    if all(abs(a - b) < 0.001 for a, b in zip(child_world_pos, expected_world_pos)):
        print("✅ Hierarchical world position calculation correct")
        print("👀 Gizmo should appear at child's world position (3, 0, 0)")
        
        # Force redraw
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
        
        return True
    else:
        print("❌ Hierarchical world position calculation incorrect")
        return False

def provide_sync_testing_instructions():
    """Provide instructions for manual sync testing"""
    print("\n📋 Manual Sync Testing Instructions")
    print("=" * 40)
    
    print("1. 🎯 Look at the gizmo position in the 3D viewport")
    print("2. 🖱️  Try these interactions:")
    print("   • Click and drag the red axis (X)")
    print("   • Click and drag the green axis (Y)")
    print("   • Click and drag the blue axis (Z)")
    print("3. 👀 Watch for these behaviors:")
    print("   • Gizmo should move smoothly with the object")
    print("   • No offset should develop between gizmo and object")
    print("   • Movement should feel natural and responsive")
    print("4. 🔄 Test the X/Y/Z buttons:")
    print("   • Use the X/Y/Z movement buttons")
    print("   • Gizmo should follow the object immediately")
    print("   • No lag or offset should occur")
    
    print(f"\n⚠️  If gizmo gets out of sync:")
    print(f"• Try selecting a different item and back")
    print(f"• Toggle gizmos off and on")
    print(f"• Check console for error messages")
    print(f"• Verify you're in a 3D viewport")
    
    print(f"\n✅ Expected improvements:")
    print(f"• Gizmo movement matches object movement exactly")
    print(f"• No speed difference between gizmo and object")
    print(f"• Smooth, natural interaction feel")
    print(f"• Gizmo always at correct world position")

def run_gizmo_sync_tests():
    """Run all gizmo synchronization tests"""
    print("🚀 Starting Gizmo Synchronization Tests")
    print("=" * 50)
    
    tests = [
        ("Create Sync Test Scene", create_sync_test_scene),
        ("Test Gizmo Position", test_gizmo_position_accuracy),
        ("Test Movement Sync", test_movement_synchronization),
        ("Test 3D Calculation", test_3d_movement_calculation),
        ("Test Hierarchical Sync", test_hierarchical_sync),
        ("Manual Testing Guide", provide_sync_testing_instructions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} passed")
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} error: {e}")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed >= 5:  # Allow manual guide to not be a "test"
        print("\n🎉 Gizmo synchronization tests completed!")
        print("\n🎮 The gizmo should now stay perfectly synchronized!")
        print("Try dragging the axes - no offset should develop!")
    else:
        print("\n⚠️  Some tests failed - check output above")
    
    return passed >= 5

if __name__ == "__main__":
    run_gizmo_sync_tests()
