"""
Debug script to help diagnose GLSL generation issues.
Run this in Blender's Text Editor to see detailed GLSL output.
"""

import bpy

def debug_tree_structure():
    """Print detailed tree structure"""
    print("🔍 Tree Structure Debug")
    print("=" * 50)
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ SDF tree not found")
        return
    
    tree = scene.sdf_tree
    
    print(f"Total items: {len(tree.items)}")
    print(f"Active index: {tree.active_index}")
    print()
    
    for i, item in enumerate(tree.items):
        indent = "  " * item.indent_level
        parent_info = f"(parent: {item.parent_index})" if item.parent_index >= 0 else "(root)"
        enabled_info = "✓" if item.is_enabled else "✗"
        
        print(f"{i:2d}: {indent}{enabled_info} {item.name} [{item.item_type}] {parent_info}")
        print(f"     {indent}   Boolean: {item.boolean_mode}, Smooth: {item.smooth_radius}")
        print(f"     {indent}   Location: {tuple(item.location)}")
        
        if item.item_type == 'SPHERE':
            print(f"     {indent}   Radius: {item.radius}")
        elif item.item_type == 'BOX':
            print(f"     {indent}   Size: {tuple(item.size)}")
        elif item.item_type == 'CYLINDER':
            print(f"     {indent}   Radius: {item.radius}, Height: {item.height}")
        elif item.item_type == 'GROUP':
            children = tree.get_children(i)
            print(f"     {indent}   Children: {children}")
    
    print()

def debug_root_items():
    """Print root items analysis"""
    print("🌳 Root Items Analysis")
    print("=" * 50)
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    root_items = [i for i, item in enumerate(tree.items) if item.is_enabled and item.parent_index == -1]
    
    print(f"Root items found: {len(root_items)}")
    for i, root_index in enumerate(root_items):
        item = tree.items[root_index]
        print(f"  {i}: Index {root_index} - {item.name} [{item.item_type}] - {item.boolean_mode}")
    
    print()

def debug_glsl_generation():
    """Debug GLSL generation step by step"""
    print("⚙️  GLSL Generation Debug")
    print("=" * 50)
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    try:
        # Test the main generation
        glsl = tree.generate_glsl()
        
        print("Generated GLSL:")
        print("-" * 30)
        print(glsl)
        print("-" * 30)
        print()
        
        # Analyze the GLSL
        lines = glsl.split('\n')
        print(f"GLSL has {len(lines)} lines")
        
        # Count different types of operations
        result_count = sum(1 for line in lines if 'result' in line)
        min_count = sum(1 for line in lines if 'min(' in line)
        max_count = sum(1 for line in lines if 'max(' in line)
        smin_count = sum(1 for line in lines if 'smin(' in line)
        
        print(f"Result operations: {result_count}")
        print(f"Min operations: {min_count}")
        print(f"Max operations: {max_count}")
        print(f"Smooth min operations: {smin_count}")
        
        # Check for specific patterns
        if "return" not in glsl:
            print("⚠️  Warning: No return statement found")
        
        if len(glsl.strip()) < 20:
            print("⚠️  Warning: GLSL seems very short")
        
        print()
        
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        import traceback
        traceback.print_exc()

def debug_individual_items():
    """Debug individual item GLSL generation"""
    print("🔧 Individual Item Debug")
    print("=" * 50)
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    for i, item in enumerate(tree.items):
        if not item.is_enabled:
            continue
            
        print(f"Item {i}: {item.name} [{item.item_type}]")
        
        try:
            if item.item_type == 'GROUP':
                # For groups, show children processing
                children = tree.get_children(i)
                print(f"  Children: {children}")
                if children:
                    item_glsl = tree._generate_item_glsl_simple(i)
                    print(f"  Group GLSL: {item_glsl}")
                else:
                    print("  Empty group")
            else:
                # For primitives, show basic GLSL
                primitive_glsl = item.generate_primitive_glsl()
                print(f"  Primitive GLSL: {primitive_glsl}")
                
                # Show full item GLSL (with children if any)
                item_glsl = tree._generate_item_glsl_simple(i)
                print(f"  Full GLSL: {item_glsl}")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
        
        print()

def run_full_debug():
    """Run complete debugging analysis"""
    print("🚀 Starting Full SDF Debug Analysis")
    print("=" * 60)
    
    debug_tree_structure()
    debug_root_items()
    debug_individual_items()
    debug_glsl_generation()
    
    print("🏁 Debug analysis complete")

if __name__ == "__main__":
    run_full_debug()
