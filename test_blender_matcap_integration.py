"""
Test script for Blender matcap integration.
Run this in Blender to test the integration with Blender's built-in matcaps.
"""

import bpy


def test_matcap_enumeration():
    """Test enumeration of available matcaps"""
    print("\n=== Testing Matcap Enumeration ===")
    
    try:
        from .shaders import SDFRenderer
        
        # Get available matcaps
        matcaps = SDFRenderer.get_available_matcaps()
        
        print(f"Found {len(matcaps)} available matcaps:")
        for identifier, name, description in matcaps:
            print(f"  {identifier}: {name} - {description}")
        
        if matcaps:
            print("✅ Matcap enumeration successful")
            return True
        else:
            print("❌ No matcaps found")
            return False
            
    except Exception as e:
        print(f"❌ Error in matcap enumeration test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_viewport_matcap_loading():
    """Test loading matcap from viewport"""
    print("\n=== Testing Viewport Matcap Loading ===")
    
    try:
        from .shaders import SDFRenderer
        
        # Set viewport to matcap mode first
        viewport_set = False
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                space = area.spaces[0]
                shading = space.shading
                
                # Store original settings
                original_type = shading.type
                original_light = shading.light
                original_studio_light = shading.studio_light
                
                # Set to matcap mode
                shading.type = 'SOLID'
                shading.light = 'MATCAP'
                
                print(f"Set viewport to matcap mode, current studio light: {shading.studio_light}")
                viewport_set = True
                
                # Test loading the current viewport matcap
                if SDFRenderer.load_viewport_matcap():
                    print("✅ Viewport matcap loaded successfully")
                    result = True
                else:
                    print("❌ Failed to load viewport matcap")
                    result = False
                
                # Restore original settings
                shading.type = original_type
                shading.light = original_light
                shading.studio_light = original_studio_light
                
                return result
        
        if not viewport_set:
            print("❌ No 3D viewport found")
            return False
            
    except Exception as e:
        print(f"❌ Error in viewport matcap loading test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_studio_light_switching():
    """Test switching between different studio lights"""
    print("\n=== Testing Studio Light Switching ===")
    
    try:
        from .shaders import SDFRenderer
        
        # Get available matcaps
        matcaps = SDFRenderer.get_available_matcaps()
        
        if not matcaps:
            print("❌ No matcaps available for switching test")
            return False
        
        # Test loading a few different matcaps
        test_count = min(3, len(matcaps))
        success_count = 0
        
        for i in range(test_count):
            matcap_id, matcap_name, _ = matcaps[i]
            print(f"Testing matcap: {matcap_id} ({matcap_name})")
            
            if SDFRenderer.load_viewport_matcap(matcap_id):
                print(f"  ✅ Successfully loaded {matcap_id}")
                success_count += 1
            else:
                print(f"  ❌ Failed to load {matcap_id}")
        
        if success_count > 0:
            print(f"✅ Studio light switching test passed ({success_count}/{test_count} successful)")
            return True
        else:
            print("❌ Studio light switching test failed")
            return False
            
    except Exception as e:
        print(f"❌ Error in studio light switching test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_material_property_integration():
    """Test integration with material properties"""
    print("\n=== Testing Material Property Integration ===")
    
    try:
        scene = bpy.context.scene
        
        # Check if material properties exist
        if not hasattr(scene, 'sdf') or not hasattr(scene.sdf, 'material'):
            print("❌ Material properties not found")
            return False
        
        mat = scene.sdf.material
        
        # Test matcap studio light property
        if hasattr(mat, 'matcap_studio_light'):
            print(f"Current matcap studio light: {mat.matcap_studio_light}")
            
            # Try to change it
            from .shaders import SDFRenderer
            matcaps = SDFRenderer.get_available_matcaps()
            
            if matcaps:
                original_value = mat.matcap_studio_light
                test_value = matcaps[0][0]  # First available matcap
                
                mat.matcap_studio_light = test_value
                print(f"Changed matcap studio light to: {mat.matcap_studio_light}")
                
                # Restore original value
                mat.matcap_studio_light = original_value
                
                print("✅ Material property integration successful")
                return True
            else:
                print("❌ No matcaps available for property test")
                return False
        else:
            print("❌ matcap_studio_light property not found")
            return False
            
    except Exception as e:
        print(f"❌ Error in material property integration test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_operator_integration():
    """Test the new viewport matcap operator"""
    print("\n=== Testing Operator Integration ===")
    
    try:
        # Check if the operator exists
        if hasattr(bpy.ops.sdf, 'load_viewport_matcap'):
            print("✅ load_viewport_matcap operator found")
            
            # Try to execute it (this might fail if no viewport is set up properly)
            try:
                result = bpy.ops.sdf.load_viewport_matcap()
                if result == {'FINISHED'}:
                    print("✅ Operator executed successfully")
                    return True
                else:
                    print(f"⚠️  Operator returned: {result}")
                    return True  # Still consider this a pass
            except Exception as op_error:
                print(f"⚠️  Operator execution failed (may be expected): {op_error}")
                return True  # This might be expected if viewport isn't set up
        else:
            print("❌ load_viewport_matcap operator not found")
            return False
            
    except Exception as e:
        print(f"❌ Error in operator integration test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_studio_light_path_resolution():
    """Test studio light path resolution"""
    print("\n=== Testing Studio Light Path Resolution ===")
    
    try:
        from .shaders import SDFRenderer
        
        # Test some common studio light names
        test_lights = ['basic_1.exr', 'clay_brown.exr', 'metal_carpaint.exr']
        
        found_count = 0
        for light_name in test_lights:
            path = SDFRenderer._get_studio_light_path(light_name)
            if path:
                print(f"✅ Found path for {light_name}: {path}")
                found_count += 1
            else:
                print(f"❌ No path found for {light_name}")
        
        if found_count > 0:
            print(f"✅ Studio light path resolution successful ({found_count}/{len(test_lights)} found)")
            return True
        else:
            print("❌ No studio light paths found")
            return False
            
    except Exception as e:
        print(f"❌ Error in studio light path resolution test: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_blender_matcap_tests():
    """Run all Blender matcap integration tests"""
    print("=" * 60)
    print("RUNNING BLENDER MATCAP INTEGRATION TESTS")
    print("=" * 60)
    
    tests = [
        ("Matcap Enumeration", test_matcap_enumeration),
        ("Viewport Matcap Loading", test_viewport_matcap_loading),
        ("Studio Light Switching", test_studio_light_switching),
        ("Material Property Integration", test_material_property_integration),
        ("Operator Integration", test_operator_integration),
        ("Studio Light Path Resolution", test_studio_light_path_resolution),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\nRunning: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("BLENDER MATCAP INTEGRATION TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("🎉 All Blender matcap integration tests passed!")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == len(results)


if __name__ == "__main__":
    run_all_blender_matcap_tests()
