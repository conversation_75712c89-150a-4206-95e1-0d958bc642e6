#!/usr/bin/env python3
"""
Test to verify chamfer now creates a solid cube with cut edges,
not an empty frame.
"""

import bpy

def clear_tree():
    """Clear the SDF tree"""
    scene = bpy.context.scene
    if hasattr(scene, 'sdf_tree'):
        tree = scene.sdf_tree
        tree.items.clear()
        tree.active_index = 0
        return True
    return False

def test_chamfer_solid_cube():
    """Test that chamfer creates a solid cube with cut edges, not an empty frame"""
    print("🔧 TESTING CHAMFER SOLID CUBE (NOT EMPTY FRAME)")
    print("=" * 60)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Normal box for reference
    bpy.ops.sdf.tree_add_box()
    normal_box = tree.items[-1]
    normal_box.name = "Normal Box"
    normal_box.size = (1.0, 1.0, 1.0)
    normal_box.location = (-1.5, 0.0, 0.0)
    normal_box.boolean_mode = 'UNION'
    
    # Chamfered box - should be solid with cut edges
    bpy.ops.sdf.tree_add_box()
    chamfered_box = tree.items[-1]
    chamfered_box.name = "Chamfered Box"
    chamfered_box.size = (1.0, 1.0, 1.0)
    chamfered_box.location = (1.5, 0.0, 0.0)
    chamfered_box.chamfer_size = 0.2
    chamfered_box.boolean_mode = 'UNION'
    
    print("✅ Created solid chamfer test:")
    print(f"  Left:  {normal_box.name} - Solid cube with sharp edges")
    print(f"  Right: {chamfered_box.name} - Should be SOLID cube with cut edges")
    print()
    print("🎯 Expected result:")
    print("  • Chamfered box should be SOLID (not hollow)")
    print("  • Should have the main cube body")
    print("  • Should have 45° cuts on the edges")
    print("  • Should NOT be an empty frame")
    print("  • Should NOT be just the edge cuts")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        print("\n✅ GLSL generated successfully")
        
        if "sdBoxChamfered" in glsl_code:
            print("✅ Chamfered box function found in GLSL")
            print("\n📋 Generated GLSL:")
            print("---")
            print(glsl_code)
            print("---")
            return True
        else:
            print("❌ Chamfered box function NOT found in GLSL")
            return False
            
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False

def test_chamfer_different_sizes():
    """Test chamfer with different sizes to ensure they're all solid"""
    print("\n🔧 TESTING CHAMFER DIFFERENT SIZES (ALL SOLID)")
    print("=" * 60)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    chamfer_sizes = [0.05, 0.1, 0.2, 0.3]
    
    for i, chamfer_size in enumerate(chamfer_sizes):
        bpy.ops.sdf.tree_add_box()
        box = tree.items[-1]
        box.name = f"Chamfer {chamfer_size}"
        box.size = (0.8, 0.8, 0.8)
        box.location = (i * 2.0 - 3.0, 0.0, 0.0)
        box.chamfer_size = chamfer_size
        box.boolean_mode = 'UNION'
        
        print(f"  Box {i+1}: Chamfer {chamfer_size} - Should be solid with {chamfer_size} edge cuts")
    
    print("\n🎯 Expected result:")
    print("  • ALL boxes should be solid (not hollow)")
    print("  • Each should have progressively bigger edge cuts")
    print("  • None should be empty frames")
    print("  • All should maintain the main cube body")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        
        chamfer_count = glsl_code.count("sdBoxChamfered")
        print(f"\n✅ GLSL contains {chamfer_count} chamfered boxes")
        
        if chamfer_count == len(chamfer_sizes):
            print("✅ All chamfer sizes generated correctly!")
            return True
        else:
            print(f"❌ Expected {len(chamfer_sizes)} chamfered boxes, got {chamfer_count}")
            return False
            
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False

def explain_chamfer_fix():
    """Explain what the chamfer fix should accomplish"""
    print("\n📖 CHAMFER FIX EXPLANATION")
    print("=" * 60)
    
    print("PROBLEM (Before Fix):")
    print("Chamfer was creating an empty frame:")
    print()
    print("    ┌─┐   ┐─┐")
    print("    │ │   │ │  ← Only the edge cuts")
    print("    │ │   │ │     (hollow frame)")
    print("    │ │   │ │")
    print("    └─┘   ┘─┘")
    print("      ^")
    print("   Missing cube body!")
    print()
    print("SOLUTION (After Fix):")
    print("Chamfer should create a solid cube with cut edges:")
    print()
    print("    ┌─┐─────┐─┐")
    print("    │ │█████│ │  ← Solid cube body")
    print("    │ │█████│ │     with cut edges")
    print("    │ │█████│ │")
    print("    └─┘─────┘─┘")
    print("      ^")
    print("   Solid body + cut edges!")
    print()
    print("Key changes in the fix:")
    print("• Keep the main cube body (boxDist)")
    print("• Apply chamfer cuts as subtractions")
    print("• Use max(boxDist, chamferCut) to combine them")
    print("• Chamfer cuts should be negative (inside) to subtract material")

def run_chamfer_solid_tests():
    """Run all chamfer solid tests"""
    print("🔧 TESTING CHAMFER SOLID FIX")
    print("=" * 70)
    print("Chamfer should create SOLID cubes with cut edges, not empty frames!")
    
    # Show explanation first
    explain_chamfer_fix()
    
    tests = [
        ("Chamfer Solid Cube", test_chamfer_solid_cube),
        ("Chamfer Different Sizes", test_chamfer_different_sizes),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 CHAMFER SOLID FIX TEST RESULTS:")
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Chamfer should now create solid cubes!")
        print("\n✅ WHAT SHOULD WORK NOW:")
        print("• Chamfer creates SOLID cubes (not hollow frames)")
        print("• Main cube body is preserved")
        print("• Edges are cut at 45-degree angles")
        print("• Larger chamfer = bigger cuts on edges")
        print("• No more empty frames or missing geometry")
        print("\n💡 TIP: Look for a solid cube with flat cuts on the edges")
        print("The cube should have substance, not be hollow!")
    else:
        print(f"\n⚠️ {len(results) - passed} test(s) failed.")
        print("The chamfer implementation may still need work.")
        print("\n🔍 If chamfer is still creating empty frames:")
        print("• Make sure the addon was reloaded")
        print("• Try small values first (0.05-0.1)")
        print("• Check that you see a solid cube, not just edge outlines")

if __name__ == "__main__":
    run_chamfer_solid_tests()
