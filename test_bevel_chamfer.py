#!/usr/bin/env python3
"""
Test and demonstration script for Bevel and Chamfer features.
This shows how to use the new edge modification properties.

IMPORTANT: Reload the addon first for the new properties to be available!
"""

import bpy

def clear_tree():
    """Clear the SDF tree"""
    scene = bpy.context.scene
    if hasattr(scene, 'sdf_tree'):
        tree = scene.sdf_tree
        tree.items.clear()
        tree.active_index = 0
        print("✅ Tree cleared")
        return True
    else:
        print("❌ No sdf_tree found!")
        return False

def test_bevel_properties():
    """Test if bevel and chamfer properties exist"""
    print("\n=== TESTING BEVEL/CHAMFER PROPERTIES ===")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Add a box to test properties
    bpy.ops.sdf.tree_add_box()
    
    if not tree.items:
        print("❌ No items added!")
        return False
    
    box = tree.items[-1]
    print(f"Testing properties on: {box.name}")
    
    # Check if bevel_radius property exists
    if hasattr(box, 'bevel_radius'):
        print(f"✅ bevel_radius property exists: {box.bevel_radius}")
        
        # Try to change it
        box.bevel_radius = 0.2
        print(f"✅ Set bevel_radius to: {box.bevel_radius}")
    else:
        print("❌ bevel_radius property does NOT exist!")
        return False
    
    # Check if chamfer_size property exists
    if hasattr(box, 'chamfer_size'):
        print(f"✅ chamfer_size property exists: {box.chamfer_size}")
        
        # Try to change it
        box.chamfer_size = 0.1
        print(f"✅ Set chamfer_size to: {box.chamfer_size}")
    else:
        print("❌ chamfer_size property does NOT exist!")
        return False
    
    return True

def example_beveled_box():
    """Example: Create a box with beveled edges"""
    print("\n=== BEVELED BOX EXAMPLE ===")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Add a box with bevel
    bpy.ops.sdf.tree_add_box()
    box = tree.items[-1]
    box.name = "Beveled Box"
    box.size = (1.0, 1.0, 1.0)
    box.location = (0.0, 0.0, 0.0)
    box.bevel_radius = 0.2  # Round the edges
    
    print(f"✅ Created beveled box:")
    print(f"   Size: {box.size[:]}")
    print(f"   Bevel Radius: {box.bevel_radius}")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        print("✅ GLSL generated:")
        print("---")
        print(glsl_code)
        print("---")
        
        if "sdBoxBeveled" in glsl_code:
            print("✅ GLSL uses beveled box function!")
            return True
        else:
            print("❌ GLSL does NOT use beveled box function!")
            return False
            
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False

def example_chamfered_box():
    """Example: Create a box with chamfered edges"""
    print("\n=== CHAMFERED BOX EXAMPLE ===")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Add a box with chamfer
    bpy.ops.sdf.tree_add_box()
    box = tree.items[-1]
    box.name = "Chamfered Box"
    box.size = (1.2, 1.2, 1.2)
    box.location = (0.0, 0.0, 0.0)
    box.chamfer_size = 0.15  # Cut the edges
    
    print(f"✅ Created chamfered box:")
    print(f"   Size: {box.size[:]}")
    print(f"   Chamfer Size: {box.chamfer_size}")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        print("✅ GLSL generated:")
        print("---")
        print(glsl_code)
        print("---")
        
        if "sdBoxChamfered" in glsl_code:
            print("✅ GLSL uses chamfered box function!")
            return True
        else:
            print("❌ GLSL does NOT use chamfered box function!")
            return False
            
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False

def example_beveled_cylinder():
    """Example: Create a cylinder with beveled edges"""
    print("\n=== BEVELED CYLINDER EXAMPLE ===")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Add a cylinder with bevel
    bpy.ops.sdf.tree_add_cylinder()
    cylinder = tree.items[-1]
    cylinder.name = "Beveled Cylinder"
    cylinder.radius = 0.8
    cylinder.height = 2.0
    cylinder.location = (0.0, 0.0, 0.0)
    cylinder.bevel_radius = 0.1  # Round the edges
    
    print(f"✅ Created beveled cylinder:")
    print(f"   Radius: {cylinder.radius}")
    print(f"   Height: {cylinder.height}")
    print(f"   Bevel Radius: {cylinder.bevel_radius}")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        print("✅ GLSL generated:")
        print("---")
        print(glsl_code)
        print("---")
        
        if "sdCylinderBeveled" in glsl_code:
            print("✅ GLSL uses beveled cylinder function!")
            return True
        else:
            print("❌ GLSL does NOT use beveled cylinder function!")
            return False
            
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False

def example_complex_beveled_shape():
    """Example: Create a complex shape using bevels and chamfers"""
    print("\n=== COMPLEX BEVELED SHAPE EXAMPLE ===")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Base: Beveled box
    bpy.ops.sdf.tree_add_box()
    base = tree.items[-1]
    base.name = "Beveled Base"
    base.size = (2.0, 2.0, 1.0)
    base.location = (0.0, 0.0, 0.0)
    base.bevel_radius = 0.2
    base.boolean_mode = 'UNION'
    
    # Add: Chamfered cylinder
    bpy.ops.sdf.tree_add_cylinder()
    cylinder = tree.items[-1]
    cylinder.name = "Chamfered Cylinder"
    cylinder.radius = 0.6
    cylinder.height = 1.5
    cylinder.location = (0.0, 0.0, 0.8)
    cylinder.chamfer_size = 0.1
    cylinder.boolean_mode = 'UNION'
    cylinder.smooth_radius = 0.15  # Smooth union
    
    # Subtract: Beveled hole
    bpy.ops.sdf.tree_add_cylinder()
    hole = tree.items[-1]
    hole.name = "Beveled Hole"
    hole.radius = 0.3
    hole.height = 3.0
    hole.location = (0.0, 0.0, 0.0)
    hole.bevel_radius = 0.05
    hole.boolean_mode = 'SUBTRACT'
    hole.smooth_radius = 0.1  # Smooth subtraction
    
    print("✅ Complex beveled shape created:")
    for i, item in enumerate(tree.items):
        bevel_info = f" (bevel: {item.bevel_radius})" if item.bevel_radius > 0 else ""
        chamfer_info = f" (chamfer: {item.chamfer_size})" if item.chamfer_size > 0 else ""
        smooth_info = f" (smooth: {item.smooth_radius})" if item.smooth_radius > 0 else ""
        print(f"  {i}: {item.name} - {item.boolean_mode}{bevel_info}{chamfer_info}{smooth_info}")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        print("\n✅ Complex shape GLSL generated:")
        print("---")
        print(glsl_code)
        print("---")
        
        # Check for expected functions
        checks = [
            ("sdBoxBeveled", "Beveled box"),
            ("sdCylinderChamfered", "Chamfered cylinder"),
            ("sdCylinderBeveled", "Beveled cylinder hole"),
        ]
        
        all_good = True
        for check_str, description in checks:
            if check_str in glsl_code:
                print(f"✅ {description} found")
            else:
                print(f"❌ {description} NOT found")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Complex shape GLSL generation failed: {e}")
        return False

def test_edge_cases():
    """Test edge cases and combinations"""
    print("\n=== TESTING EDGE CASES ===")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Test: Box with both bevel and chamfer (should prioritize bevel)
    bpy.ops.sdf.tree_add_box()
    box = tree.items[-1]
    box.name = "Both Bevel and Chamfer"
    box.bevel_radius = 0.1
    box.chamfer_size = 0.2  # This should be ignored
    
    print(f"Box with both bevel ({box.bevel_radius}) and chamfer ({box.chamfer_size})")
    
    try:
        glsl_code = tree.generate_glsl()
        if "sdBoxBeveled" in glsl_code and "sdBoxChamfered" not in glsl_code:
            print("✅ Bevel takes priority over chamfer")
            return True
        else:
            print("❌ Priority handling incorrect")
            return False
    except Exception as e:
        print(f"❌ Edge case test failed: {e}")
        return False

def run_bevel_chamfer_tests():
    """Run all bevel and chamfer tests"""
    print("🔧 TESTING BEVEL AND CHAMFER FEATURES")
    print("=" * 50)
    
    tests = [
        ("Property Existence", test_bevel_properties),
        ("Beveled Box", example_beveled_box),
        ("Chamfered Box", example_chamfered_box),
        ("Beveled Cylinder", example_beveled_cylinder),
        ("Complex Beveled Shape", example_complex_beveled_shape),
        ("Edge Cases", test_edge_cases),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 BEVEL/CHAMFER TEST RESULTS:")
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Bevel and chamfer features are working!")
        print("\n📖 HOW TO USE:")
        print("1. Add a primitive (Box, Cylinder work best)")
        print("2. Select it in the tree list")
        print("3. In properties, find 'Edge Modification' section")
        print("4. Adjust 'Bevel Radius' for rounded edges")
        print("5. Adjust 'Chamfer Size' for cut edges")
        print("6. Bevel takes priority if both are set")
    else:
        print(f"\n⚠️ {len(results) - passed} test(s) failed.")
        print("Make sure the addon is properly reloaded!")

if __name__ == "__main__":
    run_bevel_chamfer_tests()
