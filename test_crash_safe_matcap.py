"""
Crash-safe test script for matcap functionality.
This script tests only the basic, safe matcap features.
"""

import bpy


def test_basic_matcap_properties():
    """Test basic matcap property access without loading textures"""
    print("\n=== Testing Basic Matcap Properties ===")
    
    try:
        scene = bpy.context.scene
        
        # Check if SDF properties exist
        if not hasattr(scene, 'sdf'):
            print("❌ No SDF properties found!")
            return False
        
        # Check if material properties exist
        if not hasattr(scene.sdf, 'material'):
            print("❌ No material properties found!")
            return False
        
        mat = scene.sdf.material
        
        # Test basic property access (no texture loading)
        print(f"Base color: {mat.base_color}")
        print(f"Use matcap: {mat.use_matcap}")
        print(f"Matcap intensity: {mat.matcap_intensity}")
        print(f"Shading mode: {mat.shading_mode}")
        
        # Test property changes
        original_use_matcap = mat.use_matcap
        original_intensity = mat.matcap_intensity
        
        mat.use_matcap = True
        mat.matcap_intensity = 0.8
        
        print(f"Changed use_matcap to: {mat.use_matcap}")
        print(f"Changed intensity to: {mat.matcap_intensity}")
        
        # Restore original values
        mat.use_matcap = original_use_matcap
        mat.matcap_intensity = original_intensity
        
        print("✅ Basic matcap properties test passed")
        return True
        
    except Exception as e:
        print(f"❌ Error in basic matcap properties test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_default_matcap_creation():
    """Test creating the default matcap (safest operation)"""
    print("\n=== Testing Default Matcap Creation ===")
    
    try:
        from .shaders import SDFRenderer
        
        # Try to create default matcap
        print("Creating default matcap...")
        if SDFRenderer.create_default_matcap():
            print("✅ Default matcap created successfully")
            
            # Check if image was created in Blender
            if "SDF_Default_Matcap" in bpy.data.images:
                image = bpy.data.images["SDF_Default_Matcap"]
                print(f"✅ Matcap image found: {image.name}, size: {image.size}")
                return True
            else:
                print("❌ Matcap image not found in Blender")
                return False
        else:
            print("❌ Failed to create default matcap")
            return False
            
    except Exception as e:
        print(f"❌ Error in default matcap creation test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_safe_matcap_loading():
    """Test loading matcap from existing Blender image (safe operation)"""
    print("\n=== Testing Safe Matcap Loading ===")
    
    try:
        from .shaders import SDFRenderer
        
        # First create a default matcap
        if not SDFRenderer.create_default_matcap():
            print("❌ Failed to create default matcap for loading test")
            return False
        
        # Try to load it safely
        print("Loading matcap from Blender image...")
        if SDFRenderer.load_matcap_from_blender_image("SDF_Default_Matcap"):
            print("✅ Matcap loaded from Blender image successfully")
            return True
        else:
            print("❌ Failed to load matcap from Blender image")
            return False
            
    except Exception as e:
        print(f"❌ Error in safe matcap loading test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_renderer_safety():
    """Test that the renderer doesn't crash with matcap settings"""
    print("\n=== Testing Renderer Safety ===")
    
    try:
        from .shaders import SDFRenderer
        
        # Check if renderer exists
        if not hasattr(SDFRenderer, '_set_matcap_uniforms'):
            print("❌ _set_matcap_uniforms method not found")
            return False
        
        # Test with a mock scene
        scene = bpy.context.scene
        
        # Enable matcap for testing
        if hasattr(scene, 'sdf') and hasattr(scene.sdf, 'material'):
            mat = scene.sdf.material
            original_use_matcap = mat.use_matcap
            original_mode = mat.shading_mode
            
            try:
                mat.use_matcap = True
                mat.shading_mode = '1'  # Matcap mode
                
                # Test uniform setting (this should not crash)
                print("Testing matcap uniform setting...")
                
                # Only test if we have a shader (don't create one)
                if SDFRenderer._shader:
                    SDFRenderer._set_matcap_uniforms(scene)
                    print("✅ Matcap uniforms set without crashing")
                else:
                    print("⚠️  No shader available (expected if renderer not initialized)")
                
                # Restore original settings
                mat.use_matcap = original_use_matcap
                mat.shading_mode = original_mode
                
                return True
                
            except Exception as uniform_error:
                print(f"❌ Error in uniform setting: {uniform_error}")
                # Restore settings even on error
                try:
                    mat.use_matcap = original_use_matcap
                    mat.shading_mode = original_mode
                except:
                    pass
                return False
        else:
            print("⚠️  No material properties available")
            return True  # Not a failure, just not testable
        
    except Exception as e:
        print(f"❌ Error in renderer safety test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_operator_safety():
    """Test that operators don't crash"""
    print("\n=== Testing Operator Safety ===")
    
    try:
        # Test default matcap operator
        if hasattr(bpy.ops.sdf, 'load_default_matcap'):
            print("✅ load_default_matcap operator found")
            
            # Don't actually execute it, just check it exists
            print("⚠️  Operator execution skipped for safety")
        else:
            print("❌ load_default_matcap operator not found")
            return False
        
        # Test other operators
        operators_to_check = [
            'load_matcap_file',
            'load_matcap_from_blender',
            'load_viewport_matcap'
        ]
        
        for op_name in operators_to_check:
            if hasattr(bpy.ops.sdf, op_name):
                print(f"✅ {op_name} operator found")
            else:
                print(f"❌ {op_name} operator not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in operator safety test: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_crash_safe_tests():
    """Run only crash-safe matcap tests"""
    print("=" * 60)
    print("RUNNING CRASH-SAFE MATCAP TESTS")
    print("=" * 60)
    
    tests = [
        ("Basic Matcap Properties", test_basic_matcap_properties),
        ("Default Matcap Creation", test_default_matcap_creation),
        ("Safe Matcap Loading", test_safe_matcap_loading),
        ("Renderer Safety", test_renderer_safety),
        ("Operator Safety", test_operator_safety),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\nRunning: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("CRASH-SAFE TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("🎉 All crash-safe tests passed!")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == len(results)


if __name__ == "__main__":
    run_crash_safe_tests()
