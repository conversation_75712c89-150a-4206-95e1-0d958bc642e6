"""
Test script for interactive gizmo functionality.
Run this in Blender's Text Editor to test gizmo interaction and following.
"""

import bpy

def create_gizmo_test_scene():
    """Create a test scene for gizmo interaction"""
    print("🎬 Creating Gizmo Test Scene")
    print("=" * 40)
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ No SDF tree")
        return False
    
    tree = scene.sdf_tree
    
    # Clear and create test items
    tree.items.clear()
    tree.active_index = 0
    
    # Add a sphere at origin
    bpy.ops.sdf.tree_add_sphere()
    sphere = tree.items[0]
    sphere.name = "Gizmo Test Sphere"
    sphere.location = (0.0, 0.0, 0.0)
    sphere.radius = 1.0
    
    # Add a box offset
    bpy.ops.sdf.tree_add_box()
    box = tree.items[1]
    box.name = "Gizmo Test Box"
    box.location = (3.0, 0.0, 0.0)
    box.size = (1.0, 1.0, 1.0)
    box.boolean_mode = 'UNION'
    
    # Select the sphere
    tree.active_index = 0
    
    print(f"✅ Created test scene")
    print(f"   Sphere at: {tuple(sphere.location)}")
    print(f"   Box at: {tuple(box.location)}")
    print(f"   Active: {tree.items[tree.active_index].name}")
    
    return True

def test_gizmo_enable():
    """Test enabling gizmos"""
    print("\n🔄 Testing Gizmo Enable")
    print("=" * 40)
    
    try:
        # Enable widgets
        bpy.ops.sdf.toggle_simple_widgets()
        
        # Check state
        from .sdf_simple_widgets import _widget_state
        if _widget_state['enabled']:
            print("✅ Gizmos enabled")
            print(f"   Draw handler: {_widget_state['draw_handler'] is not None}")
            return True
        else:
            print("❌ Gizmos not enabled")
            return False
            
    except Exception as e:
        print(f"❌ Gizmo enable failed: {e}")
        return False

def test_button_movement():
    """Test that gizmo follows when using X/Y/Z buttons"""
    print("\n🖱️  Testing Button Movement and Gizmo Following")
    print("=" * 40)
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    if not tree.items:
        print("❌ No items to test")
        return False
    
    # Get initial position
    item = tree.items[tree.active_index]
    initial_pos = tuple(item.location)
    print(f"Initial position: {initial_pos}")
    
    # Test X movement
    try:
        print("Testing X-axis movement...")
        
        # Simulate the X button movement (we can't actually invoke the modal operator in script)
        # Instead, we'll manually move the item and check if viewport updates
        item.location[0] += 1.0
        
        # Force viewport update (like the button would do)
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
        
        new_pos = tuple(item.location)
        print(f"New position: {new_pos}")
        
        if new_pos[0] != initial_pos[0]:
            print("✅ X movement works")
            
            # Move back for next test
            item.location[0] = initial_pos[0]
            return True
        else:
            print("❌ X movement failed")
            return False
            
    except Exception as e:
        print(f"❌ Button movement test failed: {e}")
        return False

def test_gizmo_visibility():
    """Test that gizmo is visible in viewport"""
    print("\n👀 Testing Gizmo Visibility")
    print("=" * 40)
    
    # Check if we're in 3D viewport
    context = bpy.context
    if not context.area or context.area.type != 'VIEW_3D':
        print("⚠️  Not in 3D viewport - switch to 3D view to see gizmo")
        return True  # Not a failure, just a note
    
    print("✅ In 3D viewport")
    
    # Check if we have an active item
    item, tree = None, None
    try:
        from .sdf_simple_widgets import get_active_sdf_item
        item, tree = get_active_sdf_item()
    except Exception as e:
        print(f"❌ Error getting active item: {e}")
        return False
    
    if item:
        world_pos = item.get_world_location(tree)
        print(f"✅ Active item: {item.name}")
        print(f"   World position: {world_pos}")
        print("   Gizmo should appear as colored axes at this position")
        return True
    else:
        print("❌ No active item for gizmo")
        return False

def test_item_selection():
    """Test switching between items and gizmo following"""
    print("\n🎯 Testing Item Selection and Gizmo Following")
    print("=" * 40)
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    if len(tree.items) < 2:
        print("❌ Need at least 2 items to test selection")
        return False
    
    # Test selecting different items
    for i in range(min(2, len(tree.items))):
        tree.active_index = i
        item = tree.items[i]
        world_pos = item.get_world_location(tree)
        
        print(f"Selected item {i}: {item.name} at {world_pos}")
        
        # Force viewport redraw to update gizmo position
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
    
    print("✅ Item selection test complete")
    print("   Gizmo should move to each selected item's position")
    return True

def run_interactive_gizmo_tests():
    """Run all interactive gizmo tests"""
    print("🚀 Starting Interactive Gizmo Tests")
    print("=" * 50)
    
    tests = [
        ("Create Test Scene", create_gizmo_test_scene),
        ("Enable Gizmos", test_gizmo_enable),
        ("Test Visibility", test_gizmo_visibility),
        ("Test Button Movement", test_button_movement),
        ("Test Item Selection", test_item_selection),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} passed")
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} error: {e}")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 Interactive gizmo tests completed!")
        print("\n📋 What you should see now:")
        print("1. ✅ Colored axes (red/green/blue) in 3D viewport")
        print("2. ✅ Gizmo at the selected SDF item's position")
        print("3. ✅ Gizmo moves when you select different items")
        print("4. ✅ Gizmo follows when using X/Y/Z movement buttons")
        print("5. 🖱️  Try clicking and dragging the gizmo axes!")
        
        print(f"\n🎮 Manual testing:")
        print(f"• Click and drag the red line (X-axis)")
        print(f"• Click and drag the green line (Y-axis)")
        print(f"• Click and drag the blue line (Z-axis)")
        print(f"• Use X/Y/Z buttons and watch gizmo follow")
        print(f"• Select different items and see gizmo move")
    else:
        print("\n⚠️  Some tests failed - check output above")
    
    return passed == total

if __name__ == "__main__":
    run_interactive_gizmo_tests()
