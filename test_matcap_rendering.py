"""
Test script for matcap rendering functionality.
Run this in Blender to test the matcap implementation.
"""

import bpy


def test_matcap_setup():
    """Test basic matcap setup"""
    print("\n=== Testing Matcap Setup ===")
    
    try:
        scene = bpy.context.scene
        
        # Check if SDF properties exist
        if not hasattr(scene, 'sdf'):
            print("❌ No SDF properties found!")
            return False
        
        # Check if material properties exist
        if not hasattr(scene.sdf, 'material'):
            print("❌ No material properties found!")
            return False
        
        print("✅ SDF and material properties found")
        
        # Test material property access
        mat = scene.sdf.material
        print(f"Base color: {mat.base_color}")
        print(f"Use matcap: {mat.use_matcap}")
        print(f"Matcap intensity: {mat.matcap_intensity}")
        print(f"Shading mode: {mat.shading_mode}")
        print(f"Matcap image: {mat.matcap_image}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in matcap setup test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_default_matcap_creation():
    """Test default matcap creation"""
    print("\n=== Testing Default Matcap Creation ===")
    
    try:
        from .shaders import SDFRenderer
        
        # Try to create default matcap
        if SDFRenderer.create_default_matcap():
            print("✅ Default matcap created successfully")
            
            # Check if image was created in Blender
            if "SDF_Default_Matcap" in bpy.data.images:
                image = bpy.data.images["SDF_Default_Matcap"]
                print(f"✅ Matcap image found: {image.name}, size: {image.size}")
                return True
            else:
                print("❌ Matcap image not found in Blender")
                return False
        else:
            print("❌ Failed to create default matcap")
            return False
            
    except Exception as e:
        print(f"❌ Error in default matcap creation test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_matcap_loading():
    """Test matcap loading from Blender image"""
    print("\n=== Testing Matcap Loading ===")
    
    try:
        from .shaders import SDFRenderer
        
        # First create a default matcap
        if not SDFRenderer.create_default_matcap():
            print("❌ Failed to create default matcap for loading test")
            return False
        
        # Try to load it
        if SDFRenderer.load_matcap_from_blender_image("SDF_Default_Matcap"):
            print("✅ Matcap loaded from Blender image successfully")
            return True
        else:
            print("❌ Failed to load matcap from Blender image")
            return False
            
    except Exception as e:
        print(f"❌ Error in matcap loading test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_material_property_updates():
    """Test material property updates"""
    print("\n=== Testing Material Property Updates ===")
    
    try:
        scene = bpy.context.scene
        mat = scene.sdf.material
        
        # Test property changes
        original_use_matcap = mat.use_matcap
        original_intensity = mat.matcap_intensity
        original_mode = mat.shading_mode
        
        print(f"Original values - use_matcap: {original_use_matcap}, intensity: {original_intensity}, mode: {original_mode}")
        
        # Change properties
        mat.use_matcap = True
        mat.matcap_intensity = 0.8
        mat.shading_mode = '1'  # Matcap mode
        
        print(f"New values - use_matcap: {mat.use_matcap}, intensity: {mat.matcap_intensity}, mode: {mat.shading_mode}")
        
        # Restore original values
        mat.use_matcap = original_use_matcap
        mat.matcap_intensity = original_intensity
        mat.shading_mode = original_mode
        
        print("✅ Material property updates work correctly")
        return True
        
    except Exception as e:
        print(f"❌ Error in material property update test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_renderer_matcap_uniforms():
    """Test renderer matcap uniform setting"""
    print("\n=== Testing Renderer Matcap Uniforms ===")
    
    try:
        from .shaders import SDFRenderer
        
        # Check if renderer is available
        if not hasattr(SDFRenderer, '_set_matcap_uniforms'):
            print("❌ _set_matcap_uniforms method not found")
            return False
        
        # Enable matcap for testing
        scene = bpy.context.scene
        mat = scene.sdf.material
        mat.use_matcap = True
        mat.matcap_intensity = 0.7
        mat.shading_mode = '1'
        
        # Create a shader if needed (this would normally be done by the renderer)
        if not SDFRenderer._shader:
            print("⚠️  No shader available for uniform testing")
            return True  # This is expected if renderer isn't fully initialized
        
        # Try to set uniforms (this might fail if shader isn't bound, which is OK)
        try:
            SDFRenderer._set_matcap_uniforms(scene)
            print("✅ Matcap uniforms set successfully")
        except Exception as uniform_error:
            print(f"⚠️  Uniform setting failed (expected if shader not bound): {uniform_error}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in renderer matcap uniform test: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_matcap_tests():
    """Run all matcap tests"""
    print("=" * 50)
    print("RUNNING MATCAP RENDERING TESTS")
    print("=" * 50)
    
    tests = [
        ("Matcap Setup", test_matcap_setup),
        ("Default Matcap Creation", test_default_matcap_creation),
        ("Matcap Loading", test_matcap_loading),
        ("Material Property Updates", test_material_property_updates),
        ("Renderer Matcap Uniforms", test_renderer_matcap_uniforms),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\nRunning: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("🎉 All matcap tests passed!")
    else:
        print("⚠️  Some matcap tests failed. Check the output above for details.")
    
    return passed == len(results)


if __name__ == "__main__":
    run_all_matcap_tests()
