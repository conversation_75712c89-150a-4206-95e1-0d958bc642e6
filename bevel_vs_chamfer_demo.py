#!/usr/bin/env python3
"""
Visual demonstration of <PERSON><PERSON> vs <PERSON><PERSON><PERSON>.
This creates clear side-by-side comparisons to show the difference.
"""

import bpy

def clear_tree():
    """Clear the SDF tree"""
    scene = bpy.context.scene
    if hasattr(scene, 'sdf_tree'):
        tree = scene.sdf_tree
        tree.items.clear()
        tree.active_index = 0
        return True
    return False

def create_bevel_vs_chamfer_boxes():
    """Create side-by-side comparison of bevel vs chamfer on boxes"""
    print("📦 BEVEL VS CHAMFER - BOXES")
    print("=" * 40)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Normal box (reference)
    bpy.ops.sdf.tree_add_box()
    normal_box = tree.items[-1]
    normal_box.name = "Normal Box"
    normal_box.size = (0.8, 0.8, 0.8)
    normal_box.location = (-2.5, 0.0, 0.0)
    normal_box.boolean_mode = 'UNION'
    
    # Beveled box (rounded edges)
    bpy.ops.sdf.tree_add_box()
    beveled_box = tree.items[-1]
    beveled_box.name = "Beveled Box"
    beveled_box.size = (0.8, 0.8, 0.8)
    beveled_box.location = (0.0, 0.0, 0.0)
    beveled_box.bevel_radius = 0.15
    beveled_box.boolean_mode = 'UNION'
    
    # Chamfered box (flat cuts)
    bpy.ops.sdf.tree_add_box()
    chamfered_box = tree.items[-1]
    chamfered_box.name = "Chamfered Box"
    chamfered_box.size = (0.8, 0.8, 0.8)
    chamfered_box.location = (2.5, 0.0, 0.0)
    chamfered_box.chamfer_size = 0.15  # Same size as bevel
    chamfered_box.boolean_mode = 'UNION'
    
    print("✅ Created box comparison:")
    print(f"  Left:   {normal_box.name} - Sharp edges (reference)")
    print(f"  Center: {beveled_box.name} - Rounded edges (bevel: {beveled_box.bevel_radius})")
    print(f"  Right:  {chamfered_box.name} - Flat cuts (chamfer: {chamfered_box.chamfer_size})")
    print()
    print("🔍 Look for the difference:")
    print("  • Bevel: Smooth, curved transitions")
    print("  • Chamfer: Sharp, flat 45-degree cuts")

def create_bevel_vs_chamfer_cylinders():
    """Create side-by-side comparison of bevel vs chamfer on cylinders"""
    print("\n🛢️ BEVEL VS CHAMFER - CYLINDERS")
    print("=" * 40)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Normal cylinder (reference)
    bpy.ops.sdf.tree_add_cylinder()
    normal_cyl = tree.items[-1]
    normal_cyl.name = "Normal Cylinder"
    normal_cyl.radius = 0.6
    normal_cyl.height = 1.5
    normal_cyl.location = (-2.0, 0.0, 0.0)
    normal_cyl.boolean_mode = 'UNION'
    
    # Beveled cylinder (rounded edges)
    bpy.ops.sdf.tree_add_cylinder()
    beveled_cyl = tree.items[-1]
    beveled_cyl.name = "Beveled Cylinder"
    beveled_cyl.radius = 0.6
    beveled_cyl.height = 1.5
    beveled_cyl.location = (0.0, 0.0, 0.0)
    beveled_cyl.bevel_radius = 0.1
    beveled_cyl.boolean_mode = 'UNION'
    
    # Chamfered cylinder (flat cuts)
    bpy.ops.sdf.tree_add_cylinder()
    chamfered_cyl = tree.items[-1]
    chamfered_cyl.name = "Chamfered Cylinder"
    chamfered_cyl.radius = 0.6
    chamfered_cyl.height = 1.5
    chamfered_cyl.location = (2.0, 0.0, 0.0)
    chamfered_cyl.chamfer_size = 0.1  # Same size as bevel
    chamfered_cyl.boolean_mode = 'UNION'
    
    print("✅ Created cylinder comparison:")
    print(f"  Left:   {normal_cyl.name} - Sharp edges (reference)")
    print(f"  Center: {beveled_cyl.name} - Rounded edges (bevel: {beveled_cyl.bevel_radius})")
    print(f"  Right:  {chamfered_cyl.name} - Flat cuts (chamfer: {chamfered_cyl.chamfer_size})")
    print()
    print("🔍 Look for the difference:")
    print("  • Bevel: Smooth, curved edge transitions")
    print("  • Chamfer: Sharp, flat cuts at top and bottom edges")

def create_progressive_comparison():
    """Create progressive comparison showing different amounts"""
    print("\n📊 PROGRESSIVE COMPARISON")
    print("=" * 40)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    sizes = [0.05, 0.1, 0.2, 0.3]
    
    print("Creating progressive bevel comparison:")
    for i, size in enumerate(sizes):
        bpy.ops.sdf.tree_add_box()
        box = tree.items[-1]
        box.name = f"Bevel {size}"
        box.size = (0.6, 0.6, 0.6)
        box.location = (i * 1.5 - 2.25, 1.5, 0.0)
        box.bevel_radius = size
        box.boolean_mode = 'UNION'
        print(f"  Bevel {size} at position {box.location[0]}")
    
    print("\nCreating progressive chamfer comparison:")
    for i, size in enumerate(sizes):
        bpy.ops.sdf.tree_add_box()
        box = tree.items[-1]
        box.name = f"Chamfer {size}"
        box.size = (0.6, 0.6, 0.6)
        box.location = (i * 1.5 - 2.25, -1.5, 0.0)
        box.chamfer_size = size
        box.boolean_mode = 'UNION'
        print(f"  Chamfer {size} at position {box.location[0]}")
    
    print("\n🔍 Compare top row (bevel) vs bottom row (chamfer):")
    print("  • Same sizes, different edge treatments")
    print("  • Top: Rounded edges get more curved")
    print("  • Bottom: Flat cuts get bigger")

def create_mechanical_example():
    """Create a mechanical part example using both bevel and chamfer"""
    print("\n🔧 MECHANICAL PART EXAMPLE")
    print("=" * 40)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Base block (chamfered for machined look)
    bpy.ops.sdf.tree_add_box()
    base = tree.items[-1]
    base.name = "Base Block"
    base.size = (2.0, 1.5, 0.5)
    base.location = (0.0, 0.0, -0.5)
    base.chamfer_size = 0.05  # Small chamfer for machined edges
    base.boolean_mode = 'UNION'
    
    # Mounting boss (beveled for smooth connection)
    bpy.ops.sdf.tree_add_cylinder()
    boss = tree.items[-1]
    boss.name = "Mounting Boss"
    boss.radius = 0.4
    boss.height = 0.8
    boss.location = (0.0, 0.0, 0.4)
    boss.bevel_radius = 0.08  # Bevel for smooth transition
    boss.boolean_mode = 'UNION'
    boss.smooth_radius = 0.1  # Smooth union with base
    
    # Mounting holes (chamfered for realistic machining)
    for i, pos in enumerate([(-0.8, 0.6, -0.5), (0.8, 0.6, -0.5), (-0.8, -0.6, -0.5), (0.8, -0.6, -0.5)]):
        bpy.ops.sdf.tree_add_cylinder()
        hole = tree.items[-1]
        hole.name = f"Mounting Hole {i+1}"
        hole.radius = 0.15
        hole.height = 1.0
        hole.location = pos
        hole.chamfer_size = 0.02  # Small chamfer on holes
        hole.boolean_mode = 'SUBTRACT'
        hole.smooth_radius = 0.01  # Slight smooth subtraction
    
    print("✅ Created mechanical part example:")
    print(f"  Base: {base.name} - Chamfered edges (machined look)")
    print(f"  Boss: {boss.name} - Beveled edges (smooth transition)")
    print("  Holes: Chamfered edges (realistic machining)")
    print()
    print("🎯 This shows practical use:")
    print("  • Chamfer: Sharp, precise machined edges")
    print("  • Bevel: Smooth, organic transitions")

def run_visual_demo():
    """Run the visual demonstration"""
    print("🎨 BEVEL VS CHAMFER VISUAL DEMONSTRATION")
    print("=" * 60)
    print("This demo shows the clear difference between bevel and chamfer")
    print()
    
    demos = [
        ("Box Comparison", create_bevel_vs_chamfer_boxes),
        ("Cylinder Comparison", create_bevel_vs_chamfer_cylinders),
        ("Progressive Comparison", create_progressive_comparison),
        ("Mechanical Example", create_mechanical_example),
    ]
    
    print("Choose a demo to run:")
    for i, (name, _) in enumerate(demos, 1):
        print(f"  {i}. {name}")
    
    try:
        choice = input("\nEnter demo number (1-4) or 'all': ").strip().lower()
        
        if choice == 'all':
            for name, demo_func in demos:
                print(f"\n{'='*20} {name.upper()} {'='*20}")
                demo_func()
                input("Press Enter to continue to next demo...")
        else:
            choice_num = int(choice) - 1
            if 0 <= choice_num < len(demos):
                name, demo_func = demos[choice_num]
                print(f"\n{'='*20} {name.upper()} {'='*20}")
                demo_func()
            else:
                print("Invalid choice! Running all demos...")
                for name, demo_func in demos:
                    print(f"\n{'='*20} {name.upper()} {'='*20}")
                    demo_func()
                    
    except (ValueError, KeyboardInterrupt):
        print("Running all demos...")
        for name, demo_func in demos:
            print(f"\n{'='*20} {name.upper()} {'='*20}")
            demo_func()
    
    print("\n🎉 Visual demonstration complete!")
    print("\n📖 KEY TAKEAWAYS:")
    print("• BEVEL: Creates smooth, rounded edges (like a soft pillow)")
    print("• CHAMFER: Creates sharp, flat cuts at 45° (like a cut gem)")
    print("• Both work at the same size values")
    print("• Both keep the shape as one solid piece")
    print("• Use bevel for organic/soft looks")
    print("• Use chamfer for mechanical/precise looks")
    print()
    print("🔧 If chamfer still doesn't work correctly:")
    print("• Make sure the addon is reloaded")
    print("• Try different size values (0.05 to 0.3)")
    print("• Compare directly with bevel at the same size")

if __name__ == "__main__":
    run_visual_demo()
