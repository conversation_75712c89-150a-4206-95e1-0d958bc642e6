"""
Debug script to isolate the gizmo sync issue.
Run this in Blender's Text Editor to debug the sync problem.
"""

import bpy
from mathutils import Vector

def create_debug_scene():
    """Create a simple debug scene"""
    print("🔧 Creating Debug Scene")
    print("=" * 30)
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ No SDF tree")
        return False
    
    tree = scene.sdf_tree
    
    # Clear and create one simple item
    tree.items.clear()
    tree.active_index = 0
    
    # Add a sphere at origin
    bpy.ops.sdf.tree_add_sphere()
    sphere = tree.items[0]
    sphere.name = "Debug Sphere"
    sphere.location = (0.0, 0.0, 0.0)
    sphere.radius = 1.0
    
    # Select it
    tree.active_index = 0
    
    print(f"✅ Created sphere at: {tuple(sphere.location)}")
    return True

def test_manual_movement():
    """Test manual movement to see sync behavior"""
    print("\n🖱️  Testing Manual Movement")
    print("=" * 30)
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    if not tree.items:
        print("❌ No items")
        return False
    
    item = tree.items[0]
    
    # Enable gizmos
    try:
        from .sdf_simple_widgets import _widget_state
        if not _widget_state['enabled']:
            bpy.ops.sdf.toggle_simple_widgets()
        
        print(f"Gizmos enabled: {_widget_state['enabled']}")
    except Exception as e:
        print(f"❌ Error enabling gizmos: {e}")
        return False
    
    # Test step-by-step movement
    print("\nTesting step movements:")
    
    for i in range(3):
        old_pos = tuple(item.location)
        item.location[0] += 0.5  # Move 0.5 units in X
        new_pos = tuple(item.location)
        world_pos = item.get_world_location(tree)
        
        print(f"Step {i+1}:")
        print(f"  Old: {old_pos}")
        print(f"  New: {new_pos}")
        print(f"  World: {world_pos}")
        
        # Force viewport update
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
        
        # Small delay to see the movement
        import time
        time.sleep(0.1)
    
    # Reset position
    item.location = (0.0, 0.0, 0.0)
    for area in bpy.context.screen.areas:
        if area.type == 'VIEW_3D':
            area.tag_redraw()
    
    print("✅ Manual movement test complete")
    print("👀 Watch the console during gizmo dragging for debug output")
    return True

def test_widget_state():
    """Test widget state and debug info"""
    print("\n🔍 Testing Widget State")
    print("=" * 30)
    
    try:
        from .sdf_simple_widgets import _widget_state, get_active_sdf_item
        
        print(f"Widget enabled: {_widget_state['enabled']}")
        print(f"Dragging: {_widget_state['dragging']}")
        print(f"Drag axis: {_widget_state['drag_axis']}")
        print(f"Last active item: {_widget_state['last_active_item']}")
        print(f"Last item location: {_widget_state['last_item_location']}")
        
        # Test get_active_sdf_item
        item, tree = get_active_sdf_item()
        if item:
            print(f"Active item: {item.name}")
            print(f"Item location: {tuple(item.location)}")
            print(f"World location: {item.get_world_location(tree)}")
        else:
            print("❌ No active item")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Widget state test failed: {e}")
        return False

def test_simple_drag_simulation():
    """Simulate a simple drag operation"""
    print("\n🎮 Simulating Simple Drag")
    print("=" * 30)
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    if not tree.items:
        print("❌ No items")
        return False
    
    item = tree.items[0]
    
    try:
        from .sdf_simple_widgets import _widget_state
        
        # Simulate drag start
        print("Simulating drag start...")
        _widget_state['dragging'] = True
        _widget_state['drag_axis'] = 'X'
        _widget_state['drag_start_location'] = list(item.location)
        
        initial_pos = tuple(item.location)
        print(f"Initial position: {initial_pos}")
        
        # Simulate movement
        print("Simulating movement...")
        sensitivity = 0.005
        mouse_delta = 100  # Simulate 100 pixel mouse movement
        
        new_x = _widget_state['drag_start_location'][0] + mouse_delta * sensitivity
        item.location[0] = new_x
        
        final_pos = tuple(item.location)
        print(f"Final position: {final_pos}")
        print(f"Expected X change: {mouse_delta * sensitivity}")
        print(f"Actual X change: {final_pos[0] - initial_pos[0]}")
        
        # Clean up
        _widget_state['dragging'] = False
        _widget_state['drag_axis'] = None
        _widget_state['drag_start_location'] = None
        
        # Reset position
        item.location = (0.0, 0.0, 0.0)
        
        print("✅ Drag simulation complete")
        return True
        
    except Exception as e:
        print(f"❌ Drag simulation failed: {e}")
        return False

def provide_debug_instructions():
    """Provide debugging instructions"""
    print("\n📋 Debug Instructions")
    print("=" * 30)
    
    print("1. 🎯 Enable gizmos and look at the sphere")
    print("2. 🖱️  Try dragging the red axis (X)")
    print("3. 👀 Watch the console output:")
    print("   • Look for 'X movement:' messages")
    print("   • Look for 'Drawing gizmo at:' messages")
    print("   • Compare the values")
    print("4. 🔍 Check for patterns:")
    print("   • Is the gizmo position updating correctly?")
    print("   • Is the item location changing as expected?")
    print("   • Are there any error messages?")
    
    print(f"\n🐛 Common issues to look for:")
    print(f"• Multiple movement calculations happening")
    print(f"• Gizmo drawing at wrong location")
    print(f"• Item location not updating properly")
    print(f"• Viewport not redrawing correctly")
    
    print(f"\n💡 If you see the issue:")
    print(f"• Note the exact console output")
    print(f"• Try different axes (Y, Z)")
    print(f"• Try different movement speeds")
    print(f"• Check if it happens with all items")

def run_debug_tests():
    """Run all debug tests"""
    print("🚀 Starting Gizmo Sync Debug")
    print("=" * 40)
    
    tests = [
        ("Create Debug Scene", create_debug_scene),
        ("Test Widget State", test_widget_state),
        ("Test Manual Movement", test_manual_movement),
        ("Test Drag Simulation", test_simple_drag_simulation),
        ("Debug Instructions", provide_debug_instructions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} passed")
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} error: {e}")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    print("\n🔧 Ready for debugging!")
    print("Try dragging the gizmo and watch the console output.")
    
    return True

if __name__ == "__main__":
    run_debug_tests()
