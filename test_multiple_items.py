"""
Test script to verify that multiple root items are being rendered correctly.
Run this in Blender's Text Editor to test the fix.
"""

import bpy

def test_multiple_root_items():
    """Test that multiple root items are all rendered"""
    print("🧪 Testing Multiple Root Items Rendering...")
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ SDF tree not found. Make sure the addon is enabled.")
        return False
    
    tree = scene.sdf_tree
    
    # Clear existing items
    tree.items.clear()
    tree.active_index = 0
    
    # Add multiple root items
    print("Adding multiple root items...")
    
    # Add sphere
    bpy.ops.sdf.tree_add_sphere()
    sphere = tree.items[0]
    sphere.name = "Test Sphere"
    sphere.location = (-2.0, 0.0, 0.0)
    sphere.radius = 1.0
    sphere.boolean_mode = 'UNION'
    
    # Add box
    bpy.ops.sdf.tree_add_box()
    box = tree.items[1]
    box.name = "Test Box"
    box.location = (0.0, 0.0, 0.0)
    box.size = (1.0, 1.0, 1.0)
    box.boolean_mode = 'UNION'
    
    # Add cylinder
    bpy.ops.sdf.tree_add_cylinder()
    cylinder = tree.items[2]
    cylinder.name = "Test Cylinder"
    cylinder.location = (2.0, 0.0, 0.0)
    cylinder.radius = 0.5
    cylinder.height = 2.0
    cylinder.boolean_mode = 'UNION'
    
    print(f"✅ Added {len(tree.items)} root items")
    
    # Verify all items are root items (parent_index = -1)
    root_count = sum(1 for item in tree.items if item.parent_index == -1)
    if root_count == len(tree.items):
        print("✅ All items are root items")
    else:
        print(f"❌ Expected {len(tree.items)} root items, got {root_count}")
        return False
    
    # Test GLSL generation
    print("Testing GLSL generation...")
    try:
        glsl = tree.generate_glsl()
        print(f"Generated GLSL:\n{glsl}")
        
        # Check that GLSL contains references to all items
        if "result" in glsl and "d1" in glsl and "d2" in glsl:
            print("✅ GLSL contains multiple item references")
        else:
            print("❌ GLSL doesn't seem to include all items")
            print(f"GLSL content: {glsl}")
            return False
            
        # Check for boolean operations
        if "min(" in glsl or "smin(" in glsl:
            print("✅ GLSL contains boolean operations")
        else:
            print("❌ GLSL missing boolean operations")
            return False
            
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False
    
    return True

def test_group_with_children():
    """Test that groups with children work correctly"""
    print("\n🧪 Testing Group with Children...")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Clear existing items
    tree.items.clear()
    tree.active_index = 0
    
    # Add a group
    bpy.ops.sdf.tree_add_group()
    group = tree.items[0]
    group.name = "Test Group"
    group.location = (0.0, 0.0, 0.0)
    
    # Add children to the group
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='SPHERE')
    sphere = tree.items[1]
    sphere.name = "Child Sphere"
    sphere.radius = 1.0
    
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='BOX')
    box = tree.items[2]
    box.name = "Child Box"
    box.size = (0.5, 0.5, 0.5)
    box.boolean_mode = 'SUBTRACT'
    
    print(f"✅ Created group with {len(tree.get_children(0))} children")
    
    # Test GLSL generation
    try:
        glsl = tree.generate_glsl()
        print(f"Group GLSL:\n{glsl}")
        
        if len(glsl) > 20:  # Should be substantial
            print("✅ Group GLSL generated successfully")
        else:
            print("❌ Group GLSL too simple")
            return False
            
    except Exception as e:
        print(f"❌ Group GLSL generation failed: {e}")
        return False
    
    return True

def test_mixed_hierarchy():
    """Test mixed root items and groups"""
    print("\n🧪 Testing Mixed Hierarchy...")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Clear existing items
    tree.items.clear()
    tree.active_index = 0
    
    # Add root sphere
    bpy.ops.sdf.tree_add_sphere()
    root_sphere = tree.items[0]
    root_sphere.name = "Root Sphere"
    root_sphere.location = (-3.0, 0.0, 0.0)
    
    # Add group
    bpy.ops.sdf.tree_add_group()
    group = tree.items[1]
    group.name = "Mixed Group"
    group.location = (0.0, 0.0, 0.0)
    group.boolean_mode = 'UNION'
    
    # Add child to group
    tree.active_index = 1
    bpy.ops.sdf.tree_add_as_child(item_type='BOX')
    child_box = tree.items[2]
    child_box.name = "Group Child"
    
    # Add another root item
    bpy.ops.sdf.tree_add_cylinder()
    root_cylinder = tree.items[3]
    root_cylinder.name = "Root Cylinder"
    root_cylinder.location = (3.0, 0.0, 0.0)
    root_cylinder.boolean_mode = 'SUBTRACT'
    
    # Verify structure
    root_items = [i for i, item in enumerate(tree.items) if item.parent_index == -1]
    if len(root_items) == 3:  # sphere, group, cylinder
        print("✅ Mixed hierarchy structure correct")
    else:
        print(f"❌ Expected 3 root items, got {len(root_items)}")
        return False
    
    # Test GLSL
    try:
        glsl = tree.generate_glsl()
        print(f"Mixed GLSL:\n{glsl}")
        
        # Should have multiple operations
        if "result" in glsl and ("d1" in glsl or "d2" in glsl):
            print("✅ Mixed hierarchy GLSL looks correct")
        else:
            print("❌ Mixed hierarchy GLSL seems incomplete")
            return False
            
    except Exception as e:
        print(f"❌ Mixed hierarchy GLSL failed: {e}")
        return False
    
    return True

def run_all_tests():
    """Run all rendering tests"""
    print("🚀 Starting Multiple Items Rendering Tests")
    
    tests = [
        test_multiple_root_items,
        test_group_with_children,
        test_mixed_hierarchy,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ Test passed")
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"❌ Test error: {e}")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All rendering tests passed!")
        return True
    else:
        print("⚠️  Some tests failed. Check the GLSL generation.")
        return False

if __name__ == "__main__":
    run_all_tests()
