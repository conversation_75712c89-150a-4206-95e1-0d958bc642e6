#!/usr/bin/env python3
"""
Test script to verify the smooth subtraction fix.
This tests that smooth radius works correctly for subtract operations.
"""

import bpy

def clear_tree():
    """Clear the SDF tree"""
    scene = bpy.context.scene
    if hasattr(scene, 'sdf_tree'):
        tree = scene.sdf_tree
        tree.items.clear()
        tree.active_index = 0
        print("✅ Tree cleared")
        return True
    else:
        print("❌ No sdf_tree found!")
        return False

def test_regular_subtraction():
    """Test regular (non-smooth) subtraction"""
    print("\n=== TESTING REGULAR SUBTRACTION ===")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Add base sphere
    bpy.ops.sdf.tree_add_sphere()
    base = tree.items[-1]
    base.name = "Base Sphere"
    base.radius = 1.5
    base.location = (0.0, 0.0, 0.0)
    base.boolean_mode = 'UNION'
    
    # Add hole sphere (no smooth radius)
    bpy.ops.sdf.tree_add_sphere()
    hole = tree.items[-1]
    hole.name = "Hole Sphere"
    hole.radius = 0.8
    hole.location = (0.0, 0.0, 0.0)
    hole.boolean_mode = 'SUBTRACT'
    hole.smooth_radius = 0.0  # No smoothing
    
    print(f"Base: {base.name} - {base.boolean_mode}")
    print(f"Hole: {hole.name} - {hole.boolean_mode} (smooth: {hole.smooth_radius})")
    
    # Generate GLSL
    try:
        glsl_code = tree.generate_glsl()
        print("✅ GLSL generated:")
        print("---")
        print(glsl_code)
        print("---")
        
        # Should contain "max(result, -d1)"
        if "max(result, -d1)" in glsl_code:
            print("✅ Regular subtraction GLSL is correct!")
            return True
        else:
            print("❌ Regular subtraction GLSL is incorrect!")
            return False
            
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False

def test_smooth_subtraction():
    """Test smooth subtraction"""
    print("\n=== TESTING SMOOTH SUBTRACTION ===")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    if len(tree.items) >= 2:
        # Set smooth radius on the hole
        hole = tree.items[1]
        hole.smooth_radius = 0.3
        print(f"✅ Set smooth radius to {hole.smooth_radius}")
        
        # Generate GLSL
        try:
            glsl_code = tree.generate_glsl()
            print("✅ Smooth GLSL generated:")
            print("---")
            print(glsl_code)
            print("---")
            
            # Should contain "ssubtract(result, d1, 0.3)"
            if "ssubtract(result, d1" in glsl_code:
                print("✅ Smooth subtraction GLSL is correct!")
                return True
            else:
                print("❌ Smooth subtraction GLSL is incorrect!")
                print("Expected: ssubtract(result, d1, 0.3)")
                return False
                
        except Exception as e:
            print(f"❌ GLSL generation failed: {e}")
            return False
    else:
        print("❌ Need at least 2 items for smooth subtraction test")
        return False

def test_different_smooth_values():
    """Test different smooth radius values"""
    print("\n=== TESTING DIFFERENT SMOOTH VALUES ===")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    if len(tree.items) >= 2:
        hole = tree.items[1]
        
        test_values = [0.0, 0.1, 0.5, 1.0]
        
        for smooth_val in test_values:
            hole.smooth_radius = smooth_val
            print(f"\nTesting smooth radius: {smooth_val}")
            
            try:
                glsl_code = tree.generate_glsl()
                
                if smooth_val == 0.0:
                    # Should use regular subtraction
                    if "max(result, -d1)" in glsl_code:
                        print(f"✅ Smooth radius {smooth_val}: Uses regular subtraction")
                    else:
                        print(f"❌ Smooth radius {smooth_val}: Should use regular subtraction")
                else:
                    # Should use smooth subtraction
                    if f"ssubtract(result, d1, {smooth_val})" in glsl_code:
                        print(f"✅ Smooth radius {smooth_val}: Uses smooth subtraction")
                    else:
                        print(f"❌ Smooth radius {smooth_val}: Should use smooth subtraction")
                        
            except Exception as e:
                print(f"❌ GLSL generation failed for smooth radius {smooth_val}: {e}")
        
        return True
    else:
        print("❌ Need at least 2 items for this test")
        return False

def test_complex_scene():
    """Test a more complex scene with multiple operations"""
    print("\n=== TESTING COMPLEX SCENE ===")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Base sphere
    bpy.ops.sdf.tree_add_sphere()
    base = tree.items[-1]
    base.name = "Base"
    base.radius = 2.0
    base.boolean_mode = 'UNION'
    
    # Add another sphere (union with smoothing)
    bpy.ops.sdf.tree_add_sphere()
    add_sphere = tree.items[-1]
    add_sphere.name = "Addition"
    add_sphere.radius = 1.0
    add_sphere.location = (1.5, 0.0, 0.0)
    add_sphere.boolean_mode = 'UNION'
    add_sphere.smooth_radius = 0.2
    
    # Subtract a cylinder (smooth subtraction)
    bpy.ops.sdf.tree_add_cylinder()
    hole_cyl = tree.items[-1]
    hole_cyl.name = "Hole Cylinder"
    hole_cyl.radius = 0.5
    hole_cyl.height = 5.0
    hole_cyl.boolean_mode = 'SUBTRACT'
    hole_cyl.smooth_radius = 0.1
    
    # Intersect with a box
    bpy.ops.sdf.tree_add_box()
    clip_box = tree.items[-1]
    clip_box.name = "Clipping Box"
    clip_box.size = (3.0, 3.0, 1.5)
    clip_box.boolean_mode = 'INTERSECT'
    clip_box.smooth_radius = 0.05
    
    print("Complex scene created:")
    for i, item in enumerate(tree.items):
        print(f"  {i}: {item.name} - {item.boolean_mode} (smooth: {item.smooth_radius})")
    
    # Generate GLSL
    try:
        glsl_code = tree.generate_glsl()
        print("\n✅ Complex scene GLSL generated:")
        print("---")
        print(glsl_code)
        print("---")
        
        # Check for all expected operations
        checks = [
            ("smin(result, d1", "Smooth union"),
            ("ssubtract(result, d2", "Smooth subtraction"),
            ("smax(result, d3", "Smooth intersection"),
        ]
        
        all_good = True
        for check_str, description in checks:
            if check_str in glsl_code:
                print(f"✅ {description} found")
            else:
                print(f"❌ {description} NOT found")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Complex scene GLSL generation failed: {e}")
        return False

def run_smooth_subtraction_tests():
    """Run all smooth subtraction tests"""
    print("🧪 TESTING SMOOTH SUBTRACTION FIX")
    print("=" * 50)
    
    tests = [
        ("Regular Subtraction", test_regular_subtraction),
        ("Smooth Subtraction", test_smooth_subtraction),
        ("Different Smooth Values", test_different_smooth_values),
        ("Complex Scene", test_complex_scene),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SMOOTH SUBTRACTION TEST RESULTS:")
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Smooth subtraction should now work correctly!")
        print("\n💡 The fix:")
        print("- Added specialized ssubtract() function in shader")
        print("- Fixed GLSL generation to use ssubtract() for smooth subtraction")
        print("- Regular subtraction still uses max(a, -b)")
    else:
        print(f"\n⚠️ {len(results) - passed} test(s) failed.")
        print("There may still be issues with the smooth subtraction implementation.")

if __name__ == "__main__":
    run_smooth_subtraction_tests()
