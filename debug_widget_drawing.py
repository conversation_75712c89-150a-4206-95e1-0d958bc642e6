"""
Debug script for SDF widget drawing issues.
Run this in <PERSON><PERSON><PERSON>'s Text Editor to diagnose why widgets aren't appearing.
"""

import bpy

def debug_widget_system():
    """Debug the widget system step by step"""
    print("🔍 Debugging SDF Widget System")
    print("=" * 50)
    
    # Step 1: Check if SDF tree exists
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ No SDF tree found in scene")
        return False
    
    tree = scene.sdf_tree
    print(f"✅ SDF tree found with {len(tree.items)} items")
    
    # Step 2: Check if we have items
    if not tree.items:
        print("❌ No items in SDF tree")
        print("Creating test item...")
        
        # Create a test item
        bpy.ops.sdf.tree_add_sphere()
        sphere = tree.items[0]
        sphere.name = "Debug Sphere"
        sphere.location = (0.0, 0.0, 0.0)
        print(f"✅ Created test sphere: {sphere.name}")
    
    # Step 3: Check active item
    if tree.active_index < 0 or tree.active_index >= len(tree.items):
        print(f"❌ Invalid active index: {tree.active_index}")
        tree.active_index = 0
        print(f"✅ Set active index to 0")
    
    active_item = tree.items[tree.active_index]
    print(f"✅ Active item: {active_item.name} ({active_item.item_type})")
    print(f"   Local location: {tuple(active_item.location)}")
    print(f"   World location: {active_item.get_world_location(tree)}")
    
    # Step 4: Test widget functions
    try:
        from .sdf_simple_widgets import get_active_sdf_item, _widget_state
        
        item, tree_ref = get_active_sdf_item()
        if item:
            print(f"✅ get_active_sdf_item() works: {item.name}")
        else:
            print("❌ get_active_sdf_item() returned None")
            return False
        
        print(f"Widget state: enabled={_widget_state['enabled']}, handler={_widget_state['draw_handler']}")
        
    except Exception as e:
        print(f"❌ Error importing widget functions: {e}")
        return False
    
    # Step 5: Test widget enable/disable
    try:
        print("\nTesting widget enable/disable...")
        bpy.ops.sdf.toggle_simple_widgets()
        print("✅ Toggle widgets operator works")
        
        # Check state after toggle
        print(f"Widget state after toggle: enabled={_widget_state['enabled']}")
        
    except Exception as e:
        print(f"❌ Error toggling widgets: {e}")
        return False
    
    # Step 6: Test debug operator
    try:
        print("\nRunning debug operator...")
        bpy.ops.sdf.debug_widgets()
        print("✅ Debug operator completed")
        
    except Exception as e:
        print(f"❌ Error running debug operator: {e}")
        return False
    
    return True

def test_manual_drawing():
    """Test manual widget drawing"""
    print("\n🎨 Testing Manual Widget Drawing")
    print("=" * 50)
    
    try:
        from .sdf_simple_widgets import draw_simple_widget, get_active_sdf_item
        
        # Check if we have an active item
        item, tree = get_active_sdf_item()
        if not item:
            print("❌ No active item for drawing test")
            return False
        
        print(f"Drawing widget for: {item.name}")
        print(f"World location: {item.get_world_location(tree)}")
        
        # Try to draw (this might fail outside viewport context)
        try:
            draw_simple_widget()
            print("✅ Manual drawing completed (no errors)")
        except Exception as e:
            print(f"⚠️  Manual drawing failed (expected outside viewport): {e}")
            # This is expected when not in viewport drawing context
        
        return True
        
    except Exception as e:
        print(f"❌ Error in manual drawing test: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_viewport_setup():
    """Check viewport and drawing setup"""
    print("\n🖥️  Checking Viewport Setup")
    print("=" * 50)
    
    # Check if we're in a 3D viewport
    context = bpy.context
    if context.area and context.area.type == 'VIEW_3D':
        print("✅ Currently in 3D viewport")
        print(f"   Region: {context.region}")
        print(f"   Region data: {context.region_data}")
    else:
        print("⚠️  Not currently in 3D viewport")
        print(f"   Current area type: {context.area.type if context.area else 'None'}")
    
    # Check for existing draw handlers
    try:
        handlers = bpy.types.SpaceView3D.draw_handler_add.__doc__
        print("✅ Draw handler system available")
    except Exception as e:
        print(f"❌ Draw handler system issue: {e}")
    
    # Check GPU module
    try:
        import gpu
        from gpu_extras.batch import batch_for_shader
        print("✅ GPU module available")
        
        # Test basic shader creation
        shader = gpu.shader.from_builtin('3D_UNIFORM_COLOR')
        print("✅ Basic shader creation works")
        
    except Exception as e:
        print(f"❌ GPU module issue: {e}")
        return False
    
    return True

def force_viewport_redraw():
    """Force all 3D viewports to redraw"""
    print("\n🔄 Forcing Viewport Redraw")
    print("=" * 50)
    
    redraw_count = 0
    for area in bpy.context.screen.areas:
        if area.type == 'VIEW_3D':
            area.tag_redraw()
            redraw_count += 1
    
    print(f"✅ Tagged {redraw_count} 3D viewports for redraw")
    
    # Also try to update the scene
    bpy.context.view_layer.update()
    print("✅ Updated view layer")

def run_complete_debug():
    """Run complete widget debugging"""
    print("🚀 Starting Complete Widget Debug")
    print("=" * 60)
    
    success = True
    
    # Run all debug tests
    tests = [
        ("Widget System", debug_widget_system),
        ("Viewport Setup", check_viewport_setup),
        ("Manual Drawing", test_manual_drawing),
    ]
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if not test_func():
                success = False
                print(f"❌ {test_name} failed")
            else:
                print(f"✅ {test_name} passed")
        except Exception as e:
            print(f"❌ {test_name} error: {e}")
            success = False
    
    # Force redraw at the end
    force_viewport_redraw()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Debug completed - check console output above")
        print("\n📋 Next steps:")
        print("1. Check if widgets are now visible in 3D viewport")
        print("2. Try the 'Toggle Transform Widgets' button")
        print("3. Select different SDF items in the tree")
        print("4. Look for colored axes at item positions")
    else:
        print("⚠️  Some debug tests failed - check errors above")
    
    return success

if __name__ == "__main__":
    run_complete_debug()
