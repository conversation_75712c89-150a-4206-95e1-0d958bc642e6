"""
Test script for improved axis clicking functionality.
Run this in Blender's Text Editor to test clicking along the colored axis lines.
"""

import bpy

def create_axis_test_scene():
    """Create a test scene for axis clicking"""
    print("🎬 Creating Axis Clicking Test Scene")
    print("=" * 40)
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ No SDF tree")
        return False
    
    tree = scene.sdf_tree
    
    # Clear and create test items
    tree.items.clear()
    tree.active_index = 0
    
    # Add a sphere at origin for easy testing
    bpy.ops.sdf.tree_add_sphere()
    sphere = tree.items[0]
    sphere.name = "Axis Test Sphere"
    sphere.location = (0.0, 0.0, 0.0)
    sphere.radius = 1.0
    
    # Select it
    tree.active_index = 0
    
    print(f"✅ Created test sphere at origin: {tuple(sphere.location)}")
    print("   This makes it easy to see the colored axes")
    
    return True

def test_axis_detection_math():
    """Test the mathematical functions for axis detection"""
    print("\n🧮 Testing Axis Detection Math")
    print("=" * 40)
    
    try:
        # Import the detection functions
        from .sdf_simple_widgets import SDF_OT_GizmoInteraction
        
        # Create a test instance
        gizmo_op = SDF_OT_GizmoInteraction()
        
        # Test point-to-line distance calculation
        from mathutils import Vector
        
        # Test case 1: Point on line
        point = Vector((50, 50))
        line_start = Vector((0, 0))
        line_end = Vector((100, 100))
        
        distance = gizmo_op.point_to_line_distance_2d(point, line_start, line_end)
        print(f"Point on diagonal line distance: {distance:.2f} (should be ~0)")
        
        # Test case 2: Point off line
        point = Vector((50, 0))
        distance = gizmo_op.point_to_line_distance_2d(point, line_start, line_end)
        print(f"Point off diagonal line distance: {distance:.2f} (should be >0)")
        
        # Test case 3: Point on line segment check
        point = Vector((50, 50))
        on_segment = gizmo_op.point_on_line_segment_2d(point, line_start, line_end)
        print(f"Point on line segment: {on_segment} (should be True)")
        
        # Test case 4: Point outside line segment
        point = Vector((150, 150))
        on_segment = gizmo_op.point_on_line_segment_2d(point, line_start, line_end)
        print(f"Point outside line segment: {on_segment} (should be False)")
        
        print("✅ Math functions working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Math test failed: {e}")
        return False

def test_gizmo_visual_improvements():
    """Test that visual improvements are working"""
    print("\n🎨 Testing Visual Improvements")
    print("=" * 40)
    
    # Check if widgets are enabled
    try:
        from .sdf_simple_widgets import _widget_state
        
        if not _widget_state['enabled']:
            print("Enabling widgets for visual test...")
            bpy.ops.sdf.toggle_simple_widgets()
        
        if _widget_state['enabled']:
            print("✅ Widgets enabled")
            print("✅ Visual improvements should be visible:")
            print("   • Thicker axis lines (6px width)")
            print("   • Crosshair indicators at axis ends")
            print("   • Brighter colors for better visibility")
            
            # Force viewport redraw
            for area in bpy.context.screen.areas:
                if area.type == 'VIEW_3D':
                    area.tag_redraw()
            
            return True
        else:
            print("❌ Could not enable widgets")
            return False
            
    except Exception as e:
        print(f"❌ Visual test failed: {e}")
        return False

def test_viewport_context():
    """Test viewport context for axis detection"""
    print("\n🖥️  Testing Viewport Context")
    print("=" * 40)
    
    context = bpy.context
    
    # Check if we're in 3D viewport
    if context.area and context.area.type == 'VIEW_3D':
        print("✅ In 3D viewport")
        print(f"   Region: {context.region}")
        print(f"   Region data: {context.region_data}")
        
        # Check if we can get screen coordinates
        try:
            import bpy_extras.view3d_utils
            from mathutils import Vector
            
            # Test converting world to screen coordinates
            world_pos = Vector((0, 0, 0))
            screen_pos = bpy_extras.view3d_utils.location_3d_to_region_2d(
                context.region, context.region_data, world_pos
            )
            
            if screen_pos:
                print(f"✅ World-to-screen conversion works: {screen_pos}")
                return True
            else:
                print("❌ World-to-screen conversion failed")
                return False
                
        except Exception as e:
            print(f"❌ Screen coordinate test failed: {e}")
            return False
    else:
        print("⚠️  Not in 3D viewport - axis clicking requires 3D view")
        print(f"   Current area: {context.area.type if context.area else 'None'}")
        return False

def provide_usage_instructions():
    """Provide clear instructions for testing axis clicking"""
    print("\n📋 How to Test Axis Clicking")
    print("=" * 40)
    
    print("1. ✅ Make sure you're in a 3D viewport")
    print("2. ✅ Look for the colored axes at the sphere:")
    print("   • Red line = X-axis (left/right)")
    print("   • Green line = Y-axis (forward/back)")
    print("   • Blue line = Z-axis (up/down)")
    print("3. ✅ Look for crosshair indicators at the end of each axis")
    print("4. 🖱️  Try clicking and dragging along the colored lines:")
    print("   • Click anywhere along the red line and drag")
    print("   • Click anywhere along the green line and drag")
    print("   • Click anywhere along the blue line and drag")
    print("5. 👀 Watch the sphere move as you drag!")
    
    print(f"\n🎯 What should happen:")
    print(f"• Clicking near any colored line should start dragging")
    print(f"• The sphere should move along the selected axis")
    print(f"• The gizmo should follow the sphere")
    print(f"• Right-click or ESC should cancel the drag")
    
    print(f"\n⚠️  If clicking doesn't work:")
    print(f"• Make sure you're clicking close to the colored lines")
    print(f"• Try clicking near the crosshair indicators at the ends")
    print(f"• Check that the gizmo interaction modal is running")
    print(f"• Look for any error messages in the console")

def run_axis_clicking_tests():
    """Run all axis clicking tests"""
    print("🚀 Starting Axis Clicking Tests")
    print("=" * 50)
    
    tests = [
        ("Create Test Scene", create_axis_test_scene),
        ("Test Math Functions", test_axis_detection_math),
        ("Test Visual Improvements", test_gizmo_visual_improvements),
        ("Test Viewport Context", test_viewport_context),
        ("Usage Instructions", provide_usage_instructions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} passed")
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} error: {e}")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed >= 4:  # Allow usage instructions to not be a "test"
        print("\n🎉 Axis clicking tests completed!")
        print("\n🎮 Ready to test improved axis clicking!")
        print("Try clicking and dragging along the colored axis lines!")
    else:
        print("\n⚠️  Some tests failed - check output above")
    
    return passed >= 4

if __name__ == "__main__":
    run_axis_clicking_tests()
