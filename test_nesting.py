#!/usr/bin/env python3
"""
Test script for the new primitive nesting functionality in Arcane SDF.
This script tests the ability to create groups and nest primitives within them.
"""

import bpy

def test_basic_nesting():
    """Test basic nesting functionality"""
    print("\n=== Testing Basic Nesting ===")
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ SDF tree not initialized")
        return False
    
    tree = scene.sdf_tree
    
    # Clear existing items
    tree.items.clear()
    tree.active_index = 0
    
    print("✅ Cleared tree")
    
    # Test 1: Create a group
    print("Creating a group...")
    bpy.ops.sdf.tree_add_group()
    
    if len(tree.items) == 1 and tree.items[0].item_type == 'GROUP':
        print("✅ Group created successfully")
    else:
        print("❌ Failed to create group")
        return False
    
    # Test 2: Add children to the group
    print("Adding sphere as child...")
    tree.active_index = 0  # Select the group
    bpy.ops.sdf.tree_add_as_child(item_type='SPHERE')
    
    if len(tree.items) == 2 and tree.items[1].parent_index == 0:
        print("✅ Sphere added as child")
    else:
        print("❌ Failed to add sphere as child")
        return False
    
    # Test 3: Add another child
    print("Adding box as child...")
    tree.active_index = 0  # Select the group again
    bpy.ops.sdf.tree_add_as_child(item_type='BOX')
    
    if len(tree.items) == 3 and tree.items[2].parent_index == 0:
        print("✅ Box added as child")
    else:
        print("❌ Failed to add box as child")
        return False
    
    # Test 4: Check hierarchy
    children = tree.get_children(0)
    if len(children) == 2:
        print("✅ Group has correct number of children")
    else:
        print(f"❌ Group has {len(children)} children, expected 2")
        return False
    
    # Test 5: Test GLSL generation
    print("Testing GLSL generation...")
    try:
        glsl = tree.generate_glsl()
        if "return" in glsl and len(glsl) > 10:
            print("✅ GLSL generated successfully")
            print(f"Generated GLSL: {glsl[:100]}...")
        else:
            print("❌ GLSL generation failed or too short")
            return False
    except Exception as e:
        print(f"❌ GLSL generation error: {e}")
        return False
    
    return True

def test_nested_groups():
    """Test nested groups (groups within groups)"""
    print("\n=== Testing Nested Groups ===")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Clear existing items
    tree.items.clear()
    tree.active_index = 0
    
    # Create parent group
    bpy.ops.sdf.tree_add_group()
    parent_group = tree.items[0]
    parent_group.name = "Parent Group"
    
    # Add child group
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='GROUP')
    child_group = tree.items[1]
    child_group.name = "Child Group"
    
    # Add primitive to child group
    tree.active_index = 1  # Select child group
    bpy.ops.sdf.tree_add_as_child(item_type='SPHERE')
    sphere = tree.items[2]
    sphere.name = "Nested Sphere"
    
    # Verify hierarchy
    if tree.items[1].parent_index == 0 and tree.items[2].parent_index == 1:
        print("✅ Nested groups created successfully")
    else:
        print("❌ Nested groups hierarchy incorrect")
        return False
    
    # Test indent levels
    if (tree.items[0].indent_level == 0 and 
        tree.items[1].indent_level == 1 and 
        tree.items[2].indent_level == 2):
        print("✅ Indent levels correct")
    else:
        print("❌ Indent levels incorrect")
        return False
    
    return True

def test_complex_hierarchy():
    """Test a more complex hierarchy with multiple levels"""
    print("\n=== Testing Complex Hierarchy ===")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Clear existing items
    tree.items.clear()
    tree.active_index = 0
    
    # Create structure:
    # Root Group
    #   ├── Sphere (Union)
    #   ├── Sub Group (Subtract)
    #   │   ├── Box
    #   │   └── Cylinder
    #   └── Torus (Union)
    
    # Root group
    bpy.ops.sdf.tree_add_group()
    tree.items[0].name = "Root Group"
    
    # Add sphere to root
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='SPHERE')
    tree.items[1].name = "Main Sphere"
    tree.items[1].boolean_mode = 'UNION'
    
    # Add sub group to root
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='GROUP')
    tree.items[2].name = "Sub Group"
    tree.items[2].boolean_mode = 'SUBTRACT'
    
    # Add box to sub group
    tree.active_index = 2
    bpy.ops.sdf.tree_add_as_child(item_type='BOX')
    tree.items[3].name = "Sub Box"
    
    # Add cylinder to sub group
    tree.active_index = 2
    bpy.ops.sdf.tree_add_as_child(item_type='CYLINDER')
    tree.items[4].name = "Sub Cylinder"
    
    # Add torus to root
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='TORUS')
    tree.items[5].name = "Final Torus"
    tree.items[5].boolean_mode = 'UNION'
    
    # Verify structure
    expected_parents = [-1, 0, 0, 2, 2, 0]  # Parent indices
    actual_parents = [item.parent_index for item in tree.items]
    
    if actual_parents == expected_parents:
        print("✅ Complex hierarchy created correctly")
    else:
        print(f"❌ Complex hierarchy incorrect. Expected: {expected_parents}, Got: {actual_parents}")
        return False
    
    # Test GLSL generation for complex hierarchy
    try:
        glsl = tree.generate_glsl()
        if len(glsl) > 50:  # Should be substantial for complex hierarchy
            print("✅ Complex GLSL generated successfully")
        else:
            print("❌ Complex GLSL too simple")
            return False
    except Exception as e:
        print(f"❌ Complex GLSL generation error: {e}")
        return False
    
    return True

def run_all_tests():
    """Run all nesting tests"""
    print("🚀 Starting Primitive Nesting Tests")
    
    tests = [
        test_basic_nesting,
        test_nested_groups,
        test_complex_hierarchy,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ Test passed")
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"❌ Test error: {e}")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All nesting tests passed!")
        return True
    else:
        print("⚠️  Some tests failed. Check implementation.")
        return False

if __name__ == "__main__":
    run_all_tests()
