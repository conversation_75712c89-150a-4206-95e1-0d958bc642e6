#!/usr/bin/env python3
"""
Simple test to verify the chamfer fix works correctly.
<PERSON>mfer should work exactly like bevel but with flat cuts instead of rounded edges.
"""

import bpy

def clear_tree():
    """Clear the SDF tree"""
    scene = bpy.context.scene
    if hasattr(scene, 'sdf_tree'):
        tree = scene.sdf_tree
        tree.items.clear()
        tree.active_index = 0
        return True
    return False

def test_chamfer_vs_bevel_comparison():
    """Create side-by-side comparison of bevel vs chamfer"""
    print("🔧 TESTING CHAMFER VS BEVEL COMPARISON")
    print("=" * 50)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Beveled box (left) - should have rounded edges
    bpy.ops.sdf.tree_add_box()
    beveled_box = tree.items[-1]
    beveled_box.name = "Beveled Box"
    beveled_box.size = (1.0, 1.0, 1.0)
    beveled_box.location = (-1.5, 0.0, 0.0)
    beveled_box.bevel_radius = 0.2
    beveled_box.boolean_mode = 'UNION'
    
    # Chamfered box (right) - should have flat cuts at same size
    bpy.ops.sdf.tree_add_box()
    chamfered_box = tree.items[-1]
    chamfered_box.name = "Chamfered Box"
    chamfered_box.size = (1.0, 1.0, 1.0)
    chamfered_box.location = (1.5, 0.0, 0.0)
    chamfered_box.chamfer_size = 0.2  # Same size as bevel for comparison
    chamfered_box.boolean_mode = 'UNION'
    
    print("✅ Created comparison:")
    print(f"  Left:  {beveled_box.name} - Rounded edges (bevel: {beveled_box.bevel_radius})")
    print(f"  Right: {chamfered_box.name} - Flat cuts (chamfer: {chamfered_box.chamfer_size})")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        print("\n✅ GLSL generated successfully")
        
        # Check for expected functions
        if "sdBoxBeveled" in glsl_code and "sdBoxChamfered" in glsl_code:
            print("✅ Both beveled and chamfered functions found in GLSL")
            print("\n📋 Generated GLSL:")
            print("---")
            print(glsl_code)
            print("---")
            return True
        else:
            print("❌ Expected functions not found in GLSL")
            print(f"Contains sdBoxBeveled: {'sdBoxBeveled' in glsl_code}")
            print(f"Contains sdBoxChamfered: {'sdBoxChamfered' in glsl_code}")
            return False
            
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False

def test_chamfer_different_sizes():
    """Test chamfer with different sizes"""
    print("\n🔧 TESTING CHAMFER WITH DIFFERENT SIZES")
    print("=" * 50)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    chamfer_values = [0.05, 0.1, 0.2, 0.3]
    
    for i, chamfer_val in enumerate(chamfer_values):
        bpy.ops.sdf.tree_add_box()
        box = tree.items[-1]
        box.name = f"Chamfer {chamfer_val}"
        box.size = (0.8, 0.8, 0.8)
        box.location = (i * 2.0 - 3.0, 0.0, 0.0)
        box.chamfer_size = chamfer_val
        box.boolean_mode = 'UNION'
        
        print(f"  Box {i+1}: Chamfer size {chamfer_val}")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        print("\n✅ Multiple chamfer sizes GLSL generated successfully")
        
        # Count how many chamfered boxes are in the GLSL
        chamfer_count = glsl_code.count("sdBoxChamfered")
        if chamfer_count == len(chamfer_values):
            print(f"✅ Found {chamfer_count} chamfered boxes in GLSL (expected {len(chamfer_values)})")
            return True
        else:
            print(f"❌ Found {chamfer_count} chamfered boxes in GLSL (expected {len(chamfer_values)})")
            return False
            
    except Exception as e:
        print(f"❌ Multiple chamfer GLSL generation failed: {e}")
        return False

def test_chamfer_cylinder():
    """Test chamfer on cylinder"""
    print("\n🔧 TESTING CHAMFER ON CYLINDER")
    print("=" * 50)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Chamfered cylinder
    bpy.ops.sdf.tree_add_cylinder()
    cylinder = tree.items[-1]
    cylinder.name = "Chamfered Cylinder"
    cylinder.radius = 0.8
    cylinder.height = 2.0
    cylinder.location = (0.0, 0.0, 0.0)
    cylinder.chamfer_size = 0.15
    
    print(f"✅ Created chamfered cylinder:")
    print(f"   Radius: {cylinder.radius}")
    print(f"   Height: {cylinder.height}")
    print(f"   Chamfer Size: {cylinder.chamfer_size}")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        print("\n✅ Chamfered cylinder GLSL generated:")
        print("---")
        print(glsl_code)
        print("---")
        
        if "sdCylinderChamfered" in glsl_code:
            print("✅ GLSL uses chamfered cylinder function!")
            return True
        else:
            print("❌ GLSL does NOT use chamfered cylinder function!")
            return False
            
    except Exception as e:
        print(f"❌ Chamfered cylinder GLSL generation failed: {e}")
        return False

def test_visual_explanation():
    """Create a visual explanation of the difference"""
    print("\n📖 VISUAL EXPLANATION")
    print("=" * 50)
    
    print("BEVEL vs CHAMFER - What's the difference?")
    print()
    print("BEVEL (Rounded):")
    print("  ╭─────╮")
    print("  │     │  ← Smooth, curved edges")
    print("  │     │")
    print("  ╰─────╯")
    print()
    print("CHAMFER (Flat cuts):")
    print("  ┌─────┐")
    print("  │     │  ← Sharp, angled cuts at 45°")
    print("  │     │")
    print("  └─────┘")
    print()
    print("Both should:")
    print("• Cut the same amount from edges")
    print("• Keep the shape as one solid piece")
    print("• Work at the same size values")
    print("• Only differ in edge treatment (curved vs flat)")

def run_chamfer_fix_tests():
    """Run all chamfer fix tests"""
    print("🔧 TESTING FIXED CHAMFER IMPLEMENTATION")
    print("=" * 60)
    print("Chamfer should work exactly like bevel but with flat cuts!")
    
    tests = [
        ("Chamfer vs Bevel Comparison", test_chamfer_vs_bevel_comparison),
        ("Chamfer Different Sizes", test_chamfer_different_sizes),
        ("Chamfer Cylinder", test_chamfer_cylinder),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            results.append((test_name, False))
    
    # Show visual explanation
    test_visual_explanation()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 CHAMFER FIX TEST RESULTS:")
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Chamfer should now work correctly!")
        print("\n✅ WHAT SHOULD WORK NOW:")
        print("• Chamfer creates flat 45-degree cuts (not rounded)")
        print("• Chamfer works exactly like bevel but with flat edges")
        print("• No more splitting or weird geometry")
        print("• Same size values work for both bevel and chamfer")
        print("• Works on both boxes and cylinders")
        print("\n💡 TIP: Try the same size value for bevel and chamfer")
        print("to see the difference between rounded and flat cuts!")
    else:
        print(f"\n⚠️ {len(results) - passed} test(s) failed.")
        print("The chamfer implementation may still need work.")

if __name__ == "__main__":
    run_chamfer_fix_tests()
