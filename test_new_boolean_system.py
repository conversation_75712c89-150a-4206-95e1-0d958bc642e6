#!/usr/bin/env python3
"""
Test script for the NEW Boolean System.
This tests the boolean_mode property approach.

IMPORTANT: You must RELOAD the addon first for the new system to work!

To reload the addon:
1. Go to Edit > Preferences > Add-ons
2. Find "Arcane SDF" 
3. Uncheck it to disable
4. Check it again to re-enable
5. Then run this script

Or run this in the Python console:
import bpy
bpy.ops.preferences.addon_disable(module="Arcane SDF")
bpy.ops.preferences.addon_enable(module="Arcane SDF")
"""

import bpy

def reload_addon():
    """Attempt to reload the addon"""
    try:
        # Try to disable and re-enable the addon
        bpy.ops.preferences.addon_disable(module="Arcane SDF")
        bpy.ops.preferences.addon_enable(module="Arcane SDF")
        print("✅ Addon reloaded successfully")
        return True
    except Exception as e:
        print(f"❌ Could not reload addon automatically: {e}")
        print("Please manually reload the addon in Preferences > Add-ons")
        return False

def clear_and_setup():
    """Clear tree and set up for testing"""
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ No sdf_tree found! Addon may not be loaded.")
        return False
    
    tree = scene.sdf_tree
    tree.items.clear()
    tree.active_index = 0
    print("✅ Tree cleared")
    return True

def test_boolean_mode_property():
    """Test if the boolean_mode property exists"""
    print("\n=== TESTING BOOLEAN_MODE PROPERTY ===")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Add a sphere
    bpy.ops.sdf.tree_add_sphere()
    
    if not tree.items:
        print("❌ No items added!")
        return False
    
    sphere = tree.items[-1]
    print(f"Added: {sphere.name}")
    
    # Check if boolean_mode property exists
    if hasattr(sphere, 'boolean_mode'):
        print(f"✅ boolean_mode property exists: {sphere.boolean_mode}")
        
        # Try to change it
        sphere.boolean_mode = 'SUBTRACT'
        print(f"✅ Changed to: {sphere.boolean_mode}")
        
        # Change back
        sphere.boolean_mode = 'UNION'
        print(f"✅ Changed back to: {sphere.boolean_mode}")
        
        return True
    else:
        print("❌ boolean_mode property does NOT exist!")
        print("   The addon needs to be reloaded!")
        return False

def test_simple_subtraction():
    """Test a simple subtraction operation"""
    print("\n=== TESTING SIMPLE SUBTRACTION ===")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Clear and add base sphere
    tree.items.clear()
    
    # Add base sphere (Union mode by default)
    bpy.ops.sdf.tree_add_sphere()
    base_sphere = tree.items[-1]
    base_sphere.name = "Base Sphere"
    base_sphere.radius = 1.5
    base_sphere.location = (0.0, 0.0, 0.0)
    print(f"✅ Added base sphere (mode: {base_sphere.boolean_mode})")
    
    # Add smaller sphere to subtract
    bpy.ops.sdf.tree_add_sphere()
    hole_sphere = tree.items[-1]
    hole_sphere.name = "Hole Sphere"
    hole_sphere.radius = 0.8
    hole_sphere.location = (0.0, 0.0, 0.0)
    hole_sphere.boolean_mode = 'SUBTRACT'  # This should cut a hole
    print(f"✅ Added hole sphere (mode: {hole_sphere.boolean_mode})")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        print("✅ GLSL generated successfully:")
        print("---")
        print(glsl_code)
        print("---")
        
        # Check if it contains subtraction logic
        if "max(result, -d1)" in glsl_code or "smin(result, -d1" in glsl_code:
            print("✅ GLSL contains subtraction logic!")
            return True
        else:
            print("❌ GLSL does NOT contain subtraction logic!")
            return False
            
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False

def test_smooth_blending():
    """Test smooth blending"""
    print("\n=== TESTING SMOOTH BLENDING ===")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    if len(tree.items) >= 2:
        # Set smooth radius on the second item
        second_item = tree.items[1]
        second_item.smooth_radius = 0.3
        print(f"✅ Set smooth radius to {second_item.smooth_radius}")
        
        # Generate GLSL to see if smooth functions are used
        try:
            glsl_code = tree.generate_glsl()
            if "smin" in glsl_code or "smax" in glsl_code:
                print("✅ GLSL contains smooth blending functions!")
                return True
            else:
                print("❌ GLSL does NOT contain smooth blending functions!")
                return False
        except Exception as e:
            print(f"❌ GLSL generation failed: {e}")
            return False
    else:
        print("❌ Need at least 2 items for smooth blending test")
        return False

def test_all_boolean_modes():
    """Test all boolean modes"""
    print("\n=== TESTING ALL BOOLEAN MODES ===")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Clear and add test items
    tree.items.clear()
    
    # Add base sphere
    bpy.ops.sdf.tree_add_sphere()
    base = tree.items[-1]
    base.name = "Base"
    base.boolean_mode = 'UNION'
    
    # Add union sphere
    bpy.ops.sdf.tree_add_sphere()
    union_item = tree.items[-1]
    union_item.name = "Union Item"
    union_item.location = (1.0, 0.0, 0.0)
    union_item.boolean_mode = 'UNION'
    
    # Add subtract sphere
    bpy.ops.sdf.tree_add_sphere()
    subtract_item = tree.items[-1]
    subtract_item.name = "Subtract Item"
    subtract_item.location = (0.0, 1.0, 0.0)
    subtract_item.boolean_mode = 'SUBTRACT'
    
    # Add intersect sphere
    bpy.ops.sdf.tree_add_sphere()
    intersect_item = tree.items[-1]
    intersect_item.name = "Intersect Item"
    intersect_item.location = (0.0, 0.0, 1.0)
    intersect_item.boolean_mode = 'INTERSECT'
    
    print("✅ Added items with all boolean modes:")
    for i, item in enumerate(tree.items):
        print(f"  {i}: {item.name} - {item.boolean_mode}")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        print("✅ GLSL generated for all modes:")
        print("---")
        print(glsl_code)
        print("---")
        return True
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False

def run_all_tests():
    """Run all tests for the new boolean system"""
    print("🧪 TESTING NEW BOOLEAN SYSTEM")
    print("=" * 50)
    
    # First try to reload addon
    print("Attempting to reload addon...")
    reload_addon()
    
    # Setup
    if not clear_and_setup():
        return
    
    # Run tests
    tests = [
        ("Boolean Mode Property", test_boolean_mode_property),
        ("Simple Subtraction", test_simple_subtraction),
        ("Smooth Blending", test_smooth_blending),
        ("All Boolean Modes", test_all_boolean_modes),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS:")
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("\n🎉 All tests passed! The new boolean system is working!")
        print("\n📖 HOW TO USE:")
        print("1. Add primitives using the buttons")
        print("2. Select a primitive in the tree list")
        print("3. Change its 'Boolean Mode' in the properties")
        print("4. Adjust 'Smooth Radius' for smooth blending")
    else:
        print(f"\n⚠️ {len(results) - passed} test(s) failed.")
        print("Make sure the addon is properly reloaded!")

if __name__ == "__main__":
    run_all_tests()
