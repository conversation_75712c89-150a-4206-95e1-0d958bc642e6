#!/usr/bin/env python3
"""
Debug script to check what's actually happening with the boolean system.
Run this from within Blender's Python console.
"""

import bpy

def debug_current_state():
    """Debug the current state of the SDF system"""
    print("=== DEBUGGING SDF BOOLEAN SYSTEM ===")
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ No sdf_tree property found!")
        return
    
    tree = scene.sdf_tree
    print(f"✅ Tree found with {len(tree.items)} items")
    
    # Check each item
    for i, item in enumerate(tree.items):
        print(f"\nItem {i}: {item.name}")
        print(f"  - Type: {item.item_type}")
        print(f"  - Parent Index: {item.parent_index}")
        print(f"  - Enabled: {item.is_enabled}")
        
        # Check if boolean_mode property exists
        if hasattr(item, 'boolean_mode'):
            print(f"  - Boolean Mode: {item.boolean_mode}")
        else:
            print("  - ❌ No boolean_mode property!")
        
        # Check if smooth_radius exists
        if hasattr(item, 'smooth_radius'):
            print(f"  - Smooth Radius: {item.smooth_radius}")
        else:
            print("  - ❌ No smooth_radius property!")

def test_glsl_generation():
    """Test GLSL generation"""
    print("\n=== TESTING GLSL GENERATION ===")
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ No sdf_tree property found!")
        return
    
    tree = scene.sdf_tree
    
    try:
        glsl_code = tree.generate_glsl()
        print("✅ GLSL Generation successful!")
        print("Generated GLSL:")
        print("-" * 40)
        print(glsl_code)
        print("-" * 40)
    except Exception as e:
        print(f"❌ GLSL Generation failed: {e}")
        import traceback
        traceback.print_exc()

def test_add_primitives():
    """Test adding primitives and check their boolean modes"""
    print("\n=== TESTING PRIMITIVE ADDITION ===")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Clear tree first
    tree.items.clear()
    tree.active_index = 0
    print("Tree cleared")
    
    # Add a sphere
    print("Adding sphere...")
    bpy.ops.sdf.tree_add_sphere()
    
    if tree.items:
        sphere = tree.items[-1]
        print(f"✅ Sphere added: {sphere.name}")
        if hasattr(sphere, 'boolean_mode'):
            print(f"  - Boolean mode: {sphere.boolean_mode}")
        else:
            print("  - ❌ No boolean_mode property!")
    
    # Add a box
    print("Adding box...")
    bpy.ops.sdf.tree_add_box()
    
    if len(tree.items) > 1:
        box = tree.items[-1]
        print(f"✅ Box added: {box.name}")
        if hasattr(box, 'boolean_mode'):
            print(f"  - Boolean mode: {box.boolean_mode}")
            # Try to change it to subtract
            box.boolean_mode = 'SUBTRACT'
            print(f"  - Changed to: {box.boolean_mode}")
        else:
            print("  - ❌ No boolean_mode property!")

def test_shader_update():
    """Test if shader updates when boolean mode changes"""
    print("\n=== TESTING SHADER UPDATE ===")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    if len(tree.items) >= 2:
        box = tree.items[-1]
        print(f"Changing {box.name} boolean mode...")
        
        # Change boolean mode and see if shader updates
        original_mode = box.boolean_mode
        new_mode = 'SUBTRACT' if original_mode != 'SUBTRACT' else 'UNION'
        
        print(f"Changing from {original_mode} to {new_mode}")
        box.boolean_mode = new_mode
        
        # Generate GLSL to see if it changed
        try:
            glsl_code = tree.generate_glsl()
            print("✅ GLSL updated:")
            print(glsl_code[:200] + "..." if len(glsl_code) > 200 else glsl_code)
        except Exception as e:
            print(f"❌ GLSL generation failed: {e}")

def check_property_registration():
    """Check if the boolean_mode property is properly registered"""
    print("\n=== CHECKING PROPERTY REGISTRATION ===")
    
    # Check if the property is in the class definition
    from . import tree_system
    
    # Get the SDFTreeItem class
    item_class = tree_system.SDFTreeItem
    
    # Check if boolean_mode is in the class annotations
    if hasattr(item_class, '__annotations__'):
        annotations = item_class.__annotations__
        print(f"Class annotations: {list(annotations.keys())}")
        
        if 'boolean_mode' in annotations:
            print("✅ boolean_mode found in class annotations")
        else:
            print("❌ boolean_mode NOT found in class annotations")
    else:
        print("❌ No annotations found in class")
    
    # Try to create a test item
    try:
        scene = bpy.context.scene
        tree = scene.sdf_tree
        
        # Add a test item
        test_item = tree.items.add()
        test_item.item_type = 'SPHERE'
        test_item.name = "Test Item"
        
        if hasattr(test_item, 'boolean_mode'):
            print(f"✅ Test item has boolean_mode: {test_item.boolean_mode}")
        else:
            print("❌ Test item does NOT have boolean_mode")
        
        # Remove test item
        tree.items.remove(len(tree.items) - 1)
        
    except Exception as e:
        print(f"❌ Error creating test item: {e}")

def run_full_debug():
    """Run all debug tests"""
    print("🔍 RUNNING FULL DEBUG OF BOOLEAN SYSTEM")
    print("=" * 50)
    
    debug_current_state()
    check_property_registration()
    test_add_primitives()
    test_glsl_generation()
    test_shader_update()
    
    print("\n" + "=" * 50)
    print("🔍 DEBUG COMPLETE")

if __name__ == "__main__":
    run_full_debug()
