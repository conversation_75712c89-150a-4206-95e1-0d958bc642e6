"""
<PERSON><PERSON><PERSON> to create arcane.png image for the SDF addon.
Run this in Blender to create the arcane background image.
"""

import bpy
import os
import math


def create_arcane_png():
    """Create a mystical arcane.png background image"""
    print("Creating arcane.png background image...")
    
    try:
        # Image settings
        width, height = 1024, 1024
        image_name = "arcane.png"
        
        # Remove existing image if it exists
        if image_name in bpy.data.images:
            bpy.data.images.remove(bpy.data.images[image_name])
        
        # Create new image
        image = bpy.data.images.new(image_name, width, height, alpha=True)
        
        # Create mystical arcane pattern
        pixels = []
        center_x, center_y = width // 2, height // 2
        
        for y in range(height):
            for x in range(width):
                # Calculate distance from center
                dx = x - center_x
                dy = y - center_y
                distance = math.sqrt(dx*dx + dy*dy)
                angle = math.atan2(dy, dx)
                
                # Normalize distance
                norm_dist = distance / (width * 0.5)
                
                # Create base mystical colors (deep purple/blue)
                r = 0.15 + 0.1 * math.sin(norm_dist * 3)
                g = 0.05 + 0.15 * math.sin(norm_dist * 2 + angle * 3)
                b = 0.4 + 0.3 * math.sin(norm_dist * 4 + angle * 2)
                
                # Add mystical circles
                circle_intensity = 0
                for radius in [0.2, 0.4, 0.6, 0.8]:
                    circle_dist = abs(norm_dist - radius)
                    if circle_dist < 0.02:
                        circle_intensity = 1.0 - (circle_dist / 0.02)
                
                # Add golden highlights for circles
                if circle_intensity > 0:
                    r += circle_intensity * 0.6
                    g += circle_intensity * 0.4
                    b += circle_intensity * 0.1
                
                # Add radial lines
                line_angle = angle % (math.pi / 6)  # 12 lines
                if line_angle < 0.05 or line_angle > (math.pi / 6 - 0.05):
                    line_intensity = 0.3
                    r += line_intensity * 0.3
                    g += line_intensity * 0.2
                    b += line_intensity * 0.1
                
                # Add mystical symbols (simplified runes)
                symbol_intensity = 0
                
                # Create some runic patterns
                rune_x = (x // 128) * 128 + 64
                rune_y = (y // 128) * 128 + 64
                rune_dx = x - rune_x
                rune_dy = y - rune_y
                rune_dist = math.sqrt(rune_dx*rune_dx + rune_dy*rune_dy)
                
                # Simple rune pattern
                if rune_dist < 30:
                    if abs(rune_dx) < 3 or abs(rune_dy) < 3:
                        symbol_intensity = 0.5
                    elif rune_dist > 20 and rune_dist < 25:
                        symbol_intensity = 0.3
                
                # Apply symbol highlighting
                if symbol_intensity > 0:
                    r += symbol_intensity * 0.4
                    g += symbol_intensity * 0.6
                    b += symbol_intensity * 0.2
                
                # Add subtle noise for texture
                noise = (hash((x//4, y//4)) % 100) / 1000.0
                r += noise
                g += noise
                b += noise
                
                # Ensure values are in valid range
                r = max(0.0, min(1.0, r))
                g = max(0.0, min(1.0, g))
                b = max(0.0, min(1.0, b))
                
                # Set alpha for transparency
                alpha = 0.8
                
                # Fade edges
                edge_dist = min(x, y, width-x, height-y) / 50.0
                if edge_dist < 1.0:
                    alpha *= edge_dist
                
                pixels.extend([r, g, b, alpha])
        
        # Set image pixels
        image.pixels = pixels
        
        # Pack the image so it's saved with the blend file
        image.pack()
        
        # Try to save as PNG file in addon directory
        try:
            addon_dir = os.path.dirname(__file__)
            save_path = os.path.join(addon_dir, "arcane.png")
            
            # Save the image
            image.filepath_raw = save_path
            image.file_format = 'PNG'
            image.save()
            
            print(f"✅ Created and saved arcane.png to: {save_path}")
        except Exception as save_error:
            print(f"⚠️  Could not save to file: {save_error}")
            print("✅ Created arcane.png in Blender (packed with blend file)")
        
        print(f"✅ Successfully created mystical arcane.png ({width}x{height})")
        return True
        
    except Exception as e:
        print(f"❌ Error creating arcane.png: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_simple_arcane():
    """Create a simpler version of arcane.png"""
    print("Creating simple arcane.png...")
    
    try:
        width, height = 512, 512
        image_name = "arcane.png"
        
        if image_name in bpy.data.images:
            bpy.data.images.remove(bpy.data.images[image_name])
        
        image = bpy.data.images.new(image_name, width, height, alpha=True)
        
        pixels = []
        center_x, center_y = width // 2, height // 2
        
        for y in range(height):
            for x in range(width):
                # Simple radial gradient
                dx = x - center_x
                dy = y - center_y
                distance = math.sqrt(dx*dx + dy*dy)
                norm_dist = distance / (width * 0.5)
                
                # Mystical purple/blue gradient
                r = 0.2 + 0.3 * (1.0 - norm_dist)
                g = 0.1 + 0.2 * (1.0 - norm_dist)
                b = 0.6 + 0.4 * (1.0 - norm_dist)
                
                # Add some pattern
                pattern = math.sin(distance * 0.1) * 0.1
                r += pattern
                g += pattern
                b += pattern
                
                # Clamp values
                r = max(0.0, min(1.0, r))
                g = max(0.0, min(1.0, g))
                b = max(0.0, min(1.0, b))
                
                pixels.extend([r, g, b, 0.7])
        
        image.pixels = pixels
        image.pack()
        
        print("✅ Created simple arcane.png")
        return True
        
    except Exception as e:
        print(f"❌ Error creating simple arcane.png: {e}")
        return False


def test_arcane_creation():
    """Test creating arcane.png"""
    print("=" * 50)
    print("CREATING ARCANE.PNG FOR SDF ADDON")
    print("=" * 50)
    
    # Try complex version first
    if create_arcane_png():
        print("✅ Complex arcane.png created successfully")
    else:
        print("⚠️  Complex version failed, trying simple version...")
        if create_simple_arcane():
            print("✅ Simple arcane.png created successfully")
        else:
            print("❌ Failed to create any version of arcane.png")
            return False
    
    # Verify the image exists
    if "arcane.png" in bpy.data.images:
        image = bpy.data.images["arcane.png"]
        print(f"✅ Verified: arcane.png exists in Blender ({image.size[0]}x{image.size[1]})")
        
        # Test if it can be used as texture
        try:
            import gpu
            texture = gpu.texture.from_image(image)
            print("✅ Successfully created GPU texture from arcane.png")
            return True
        except Exception as texture_error:
            print(f"❌ Failed to create GPU texture: {texture_error}")
            return False
    else:
        print("❌ arcane.png not found in Blender images")
        return False


if __name__ == "__main__":
    test_arcane_creation()
