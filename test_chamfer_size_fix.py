#!/usr/bin/env python3
"""
Test to verify chamfer now cuts edges instead of making primitives bigger.
This should show that chamfer works like bevel but with flat cuts.
"""

import bpy

def clear_tree():
    """Clear the SDF tree"""
    scene = bpy.context.scene
    if hasattr(scene, 'sdf_tree'):
        tree = scene.sdf_tree
        tree.items.clear()
        tree.active_index = 0
        return True
    return False

def test_chamfer_cuts_not_expands():
    """Test that chamfer cuts edges instead of expanding the primitive"""
    print("🔧 TESTING CHAMFER CUTS (NOT EXPANDS)")
    print("=" * 50)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Reference box (normal, no modifications)
    bpy.ops.sdf.tree_add_box()
    normal_box = tree.items[-1]
    normal_box.name = "Reference Box"
    normal_box.size = (1.0, 1.0, 1.0)
    normal_box.location = (-2.0, 0.0, 0.0)
    normal_box.boolean_mode = 'UNION'
    
    # Beveled box (should be smaller with rounded edges)
    bpy.ops.sdf.tree_add_box()
    beveled_box = tree.items[-1]
    beveled_box.name = "Beveled Box"
    beveled_box.size = (1.0, 1.0, 1.0)
    beveled_box.location = (0.0, 0.0, 0.0)
    beveled_box.bevel_radius = 0.2
    beveled_box.boolean_mode = 'UNION'
    
    # Chamfered box (should be smaller with flat cuts, same size as beveled)
    bpy.ops.sdf.tree_add_box()
    chamfered_box = tree.items[-1]
    chamfered_box.name = "Chamfered Box"
    chamfered_box.size = (1.0, 1.0, 1.0)
    chamfered_box.location = (2.0, 0.0, 0.0)
    chamfered_box.chamfer_size = 0.2  # Same size as bevel
    chamfered_box.boolean_mode = 'UNION'
    
    print("✅ Created size comparison:")
    print(f"  Left:   {normal_box.name} - Original size (1.0 x 1.0 x 1.0)")
    print(f"  Center: {beveled_box.name} - Should be SMALLER with rounded cuts")
    print(f"  Right:  {chamfered_box.name} - Should be SMALLER with flat cuts")
    print()
    print("🎯 Expected result:")
    print("  • All three should be different shapes")
    print("  • Beveled and chamfered should be SMALLER than normal")
    print("  • Beveled and chamfered should be SAME SIZE")
    print("  • Only difference should be edge treatment (rounded vs flat)")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        print("\n✅ GLSL generated successfully")
        
        # Check for expected functions
        checks = [
            ("sdBox(", "Normal box"),
            ("sdBoxBeveled", "Beveled box"),
            ("sdBoxChamfered", "Chamfered box"),
        ]
        
        all_found = True
        for check_str, description in checks:
            if check_str in glsl_code:
                print(f"✅ {description} found in GLSL")
            else:
                print(f"❌ {description} NOT found in GLSL")
                all_found = False
        
        if all_found:
            print("\n📋 Generated GLSL:")
            print("---")
            print(glsl_code)
            print("---")
        
        return all_found
        
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False

def test_chamfer_different_sizes():
    """Test chamfer with different sizes to ensure it's cutting, not expanding"""
    print("\n🔧 TESTING CHAMFER DIFFERENT SIZES")
    print("=" * 50)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Create boxes with increasing chamfer sizes
    chamfer_sizes = [0.0, 0.1, 0.2, 0.3]
    
    for i, chamfer_size in enumerate(chamfer_sizes):
        bpy.ops.sdf.tree_add_box()
        box = tree.items[-1]
        box.name = f"Chamfer {chamfer_size}"
        box.size = (0.8, 0.8, 0.8)
        box.location = (i * 2.0 - 3.0, 0.0, 0.0)
        box.chamfer_size = chamfer_size
        box.boolean_mode = 'UNION'
        
        print(f"  Box {i+1}: Chamfer {chamfer_size} - Should get progressively SMALLER")
    
    print("\n🎯 Expected result:")
    print("  • Box 1 (chamfer 0.0): Normal size")
    print("  • Box 2 (chamfer 0.1): Slightly smaller with small flat cuts")
    print("  • Box 3 (chamfer 0.2): More smaller with bigger flat cuts")
    print("  • Box 4 (chamfer 0.3): Much smaller with large flat cuts")
    print("  • Each box should be SMALLER than the previous one")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        
        # Count chamfered boxes (should be 3, since one has chamfer_size = 0.0)
        chamfer_count = glsl_code.count("sdBoxChamfered")
        normal_count = glsl_code.count("sdBox(") - glsl_code.count("sdBoxChamfered") - glsl_code.count("sdBoxBeveled")
        
        print(f"\n✅ GLSL contains:")
        print(f"  • {normal_count} normal box(es)")
        print(f"  • {chamfer_count} chamfered box(es)")
        
        if chamfer_count == 3 and normal_count == 1:
            print("✅ Correct number of each box type!")
            return True
        else:
            print("❌ Unexpected box counts in GLSL")
            return False
            
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False

def test_bevel_vs_chamfer_same_size():
    """Direct comparison of bevel vs chamfer at the same size"""
    print("\n🔧 TESTING BEVEL VS CHAMFER SAME SIZE")
    print("=" * 50)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    test_size = 0.15
    
    # Beveled box
    bpy.ops.sdf.tree_add_box()
    beveled_box = tree.items[-1]
    beveled_box.name = f"Bevel {test_size}"
    beveled_box.size = (1.0, 1.0, 1.0)
    beveled_box.location = (-1.0, 0.0, 0.0)
    beveled_box.bevel_radius = test_size
    beveled_box.boolean_mode = 'UNION'
    
    # Chamfered box
    bpy.ops.sdf.tree_add_box()
    chamfered_box = tree.items[-1]
    chamfered_box.name = f"Chamfer {test_size}"
    chamfered_box.size = (1.0, 1.0, 1.0)
    chamfered_box.location = (1.0, 0.0, 0.0)
    chamfered_box.chamfer_size = test_size
    chamfered_box.boolean_mode = 'UNION'
    
    print(f"✅ Created direct comparison at size {test_size}:")
    print(f"  Left:  {beveled_box.name} - Rounded edges")
    print(f"  Right: {chamfered_box.name} - Flat cuts")
    print()
    print("🎯 Expected result:")
    print("  • Both boxes should be the SAME SIZE")
    print("  • Both should be SMALLER than a normal 1x1x1 box")
    print("  • Left should have smooth, curved edges")
    print("  • Right should have sharp, flat 45-degree cuts")
    print("  • The overall 'footprint' should be identical")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        
        if "sdBoxBeveled" in glsl_code and "sdBoxChamfered" in glsl_code:
            print("\n✅ Both bevel and chamfer functions found in GLSL")
            print("✅ Direct comparison ready!")
            return True
        else:
            print("\n❌ Missing expected functions in GLSL")
            return False
            
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False

def run_chamfer_size_tests():
    """Run all chamfer size tests"""
    print("🔧 TESTING CHAMFER SIZE FIX")
    print("=" * 60)
    print("Chamfer should CUT edges (make smaller), not expand!")
    
    tests = [
        ("Chamfer Cuts Not Expands", test_chamfer_cuts_not_expands),
        ("Chamfer Different Sizes", test_chamfer_different_sizes),
        ("Bevel vs Chamfer Same Size", test_bevel_vs_chamfer_same_size),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 CHAMFER SIZE FIX TEST RESULTS:")
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Chamfer should now cut edges correctly!")
        print("\n✅ WHAT SHOULD WORK NOW:")
        print("• Chamfer makes primitives SMALLER (cuts edges)")
        print("• Chamfer works exactly like bevel but with flat cuts")
        print("• Same size values produce same overall size reduction")
        print("• Larger chamfer values = more cutting")
        print("• No more expanding or weird geometry")
        print("\n💡 TIP: Compare bevel 0.2 vs chamfer 0.2")
        print("They should be the same size with different edge treatments!")
    else:
        print(f"\n⚠️ {len(results) - passed} test(s) failed.")
        print("The chamfer implementation may still need work.")
        print("\n🔍 If chamfer is still making things bigger:")
        print("• Check that the addon was reloaded")
        print("• Try very small values first (0.05)")
        print("• Compare directly with bevel at the same size")

if __name__ == "__main__":
    run_chamfer_size_tests()
