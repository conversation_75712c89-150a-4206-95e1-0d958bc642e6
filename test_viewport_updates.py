#!/usr/bin/env python3
"""
Test script to verify viewport update fixes work correctly.
Run this from within Blender's Python console.
"""

import bpy
import time

def test_automatic_updates():
    """Test that viewport updates automatically when properties change"""
    print("=== Testing Automatic Viewport Updates ===")
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("ERROR: sdf_tree property not found!")
        return False
    
    tree = scene.sdf_tree
    
    # Ensure we have at least one item
    if not tree.items:
        tree.add_item('SPHERE', name="Test Sphere")
    
    # Get the first item
    item = tree.items[0]
    print(f"Testing with item: {item.name} ({item.item_type})")
    
    # Test property changes
    print("Changing radius...")
    original_radius = item.radius
    item.radius = original_radius + 0.5
    print(f"Radius changed from {original_radius} to {item.radius}")
    
    # Test location changes
    print("Changing location...")
    original_location = item.location[:]
    item.location = (1.0, 0.0, 0.0)
    print(f"Location changed from {original_location} to {item.location[:]}")
    
    # Test enable/disable
    print("Toggling enabled state...")
    original_enabled = item.is_enabled
    item.is_enabled = not original_enabled
    print(f"Enabled changed from {original_enabled} to {item.is_enabled}")
    item.is_enabled = original_enabled  # Restore
    
    return True

def test_add_remove_updates():
    """Test that viewport updates when items are added/removed"""
    print("\n=== Testing Add/Remove Updates ===")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    initial_count = len(tree.items)
    print(f"Initial item count: {initial_count}")
    
    # Add an item
    print("Adding a box...")
    bpy.ops.sdf.tree_add_box()
    print(f"Item count after add: {len(tree.items)}")
    
    # Remove an item
    if len(tree.items) > 1:
        print("Removing an item...")
        tree.active_index = len(tree.items) - 1  # Select last item
        bpy.ops.sdf.tree_remove_item()
        print(f"Item count after remove: {len(tree.items)}")
    
    return True

def test_shader_refresh():
    """Test manual shader refresh"""
    print("\n=== Testing Manual Shader Refresh ===")
    
    try:
        from .shaders import SDFRenderer
        
        print(f"Renderer enabled: {SDFRenderer.is_enabled()}")
        
        if not SDFRenderer.is_enabled():
            print("Enabling renderer...")
            SDFRenderer.enable()
        
        print("Refreshing shader...")
        SDFRenderer.refresh_shader()
        
        print("Forcing viewport redraw...")
        SDFRenderer._force_viewport_redraw()
        
        return True
        
    except Exception as e:
        print(f"Shader refresh test failed: {e}")
        return False

def test_timer_system():
    """Test the timer-based update system"""
    print("\n=== Testing Timer System ===")
    
    try:
        from .shaders import SDFRenderer
        
        # Check if timer is running
        timer_active = SDFRenderer._timer_handle is not None
        print(f"Timer active: {timer_active}")
        
        if SDFRenderer.is_enabled():
            print("Renderer is enabled, timer should be active")
            if not timer_active:
                print("WARNING: Timer not active despite renderer being enabled")
                return False
        
        return True
        
    except Exception as e:
        print(f"Timer system test failed: {e}")
        return False

def test_viewport_settings():
    """Test viewport settings and their effect on updates"""
    print("\n=== Testing Viewport Settings ===")
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf'):
        print("ERROR: sdf properties not found!")
        return False
    
    sdf_props = scene.sdf
    
    # Test viewport visibility toggle
    print("Testing viewport visibility toggle...")
    original_show = sdf_props.sdf_show_in_viewport
    print(f"Original show_in_viewport: {original_show}")
    
    # Toggle off
    sdf_props.sdf_show_in_viewport = False
    print(f"Set show_in_viewport to: {sdf_props.sdf_show_in_viewport}")
    
    # Toggle back on
    sdf_props.sdf_show_in_viewport = True
    print(f"Set show_in_viewport to: {sdf_props.sdf_show_in_viewport}")
    
    # Restore original
    sdf_props.sdf_show_in_viewport = original_show
    
    return True

def run_all_tests():
    """Run all viewport update tests"""
    print("Starting Viewport Update Tests...")
    
    tests = [
        ("Automatic Updates", test_automatic_updates),
        ("Add/Remove Updates", test_add_remove_updates),
        ("Shader Refresh", test_shader_refresh),
        ("Timer System", test_timer_system),
        ("Viewport Settings", test_viewport_settings),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n--- Running {test_name} ---")
            result = test_func()
            results.append((test_name, result))
            print(f"{test_name}: {'PASS' if result else 'FAIL'}")
        except Exception as e:
            print(f"{test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    print("\n=== Test Summary ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"Passed: {passed}/{total}")
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name}: {status}")
    
    if passed == total:
        print("\n✅ All tests passed! Viewport updates should work correctly.")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Check the output above for details.")

if __name__ == "__main__":
    run_all_tests()
