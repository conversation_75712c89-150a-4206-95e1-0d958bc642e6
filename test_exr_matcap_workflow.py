"""
Test script for EXR matcap workflow.
This script simulates loading an EXR file and setting it up as a matcap.
"""

import bpy
import os


def simulate_exr_loading():
    """Simulate loading an EXR file as matcap"""
    print("\n=== SIMULATING EXR MATCAP LOADING ===")
    
    try:
        # First, let's see what images are available
        print("Available images in Blender:")
        for img_name in bpy.data.images.keys():
            img = bpy.data.images[img_name]
            print(f"  {img_name}: {img.size}, filepath: {img.filepath}")
        
        # Find any EXR files
        exr_images = [name for name in bpy.data.images.keys() if name.lower().endswith('.exr')]
        
        if not exr_images:
            print("No EXR images found. Creating a test image...")
            # Create a test image to simulate an EXR
            test_image = bpy.data.images.new("test_matcap.exr", 256, 256)
            test_image.generated_type = 'UV_GRID'
            test_image.pack()
            exr_images = ["test_matcap.exr"]
            print(f"Created test image: {test_image.name}")
        
        # Use the first EXR image
        exr_name = exr_images[0]
        print(f"Using EXR image: {exr_name}")
        
        # Load it as matcap using the operator
        from .shaders import SDFRenderer
        
        print("Loading EXR as matcap...")
        if SDFRenderer.load_matcap_from_blender_image(exr_name):
            print(f"✅ Successfully loaded {exr_name} as matcap texture")
            
            # Configure material properties
            scene = bpy.context.scene
            mat = scene.sdf.material
            
            print("Configuring material properties...")
            mat.matcap_image = exr_name
            mat.use_matcap = True
            mat.shading_mode = '1'  # Matcap mode
            mat.matcap_intensity = 1.0
            mat.base_color = (1.0, 1.0, 1.0, 1.0)  # White base
            
            # Enable viewport rendering
            scene.sdf.sdf_show_in_viewport = True
            
            print("✅ Material configured for matcap rendering")
            
            # Force shader refresh
            print("Refreshing shader...")
            SDFRenderer.refresh_shader()
            
            print("✅ Shader refreshed")
            
            # Debug the current state
            print("\nCurrent matcap state:")
            print(f"  Renderer enabled: {SDFRenderer.is_enabled()}")
            print(f"  Has matcap texture: {SDFRenderer._matcap_texture is not None}")
            print(f"  Use matcap: {mat.use_matcap}")
            print(f"  Shading mode: {mat.shading_mode}")
            print(f"  Matcap intensity: {mat.matcap_intensity}")
            print(f"  Matcap image: {mat.matcap_image}")
            
            return True
        else:
            print(f"❌ Failed to load {exr_name} as matcap")
            return False
        
    except Exception as e:
        print(f"❌ Error in EXR loading simulation: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_matcap_operator_workflow():
    """Test the complete matcap operator workflow"""
    print("\n=== TESTING MATCAP OPERATOR WORKFLOW ===")
    
    try:
        # Test the load_matcap_from_blender operator
        if not hasattr(bpy.ops.sdf, 'load_matcap_from_blender'):
            print("❌ load_matcap_from_blender operator not available")
            return False
        
        # Find an image to use
        available_images = list(bpy.data.images.keys())
        if not available_images:
            print("Creating test image...")
            test_image = bpy.data.images.new("test_matcap_operator.exr", 256, 256)
            test_image.generated_type = 'COLOR_GRID'
            test_image.pack()
            test_image_name = test_image.name
        else:
            test_image_name = available_images[0]
        
        print(f"Testing operator with image: {test_image_name}")
        
        # Execute the operator (this should configure everything)
        # Note: We can't easily test the operator dialog, so we'll test the underlying function
        from .shaders import SDFRenderer
        
        # Manually call what the operator does
        if SDFRenderer.load_matcap_from_blender_image(test_image_name):
            print("✅ Operator function executed successfully")
            
            # Check if material was configured
            scene = bpy.context.scene
            mat = scene.sdf.material
            
            print(f"Material matcap_image: {mat.matcap_image}")
            print(f"Material use_matcap: {mat.use_matcap}")
            print(f"Material shading_mode: {mat.shading_mode}")
            
            if mat.use_matcap and mat.shading_mode == '1':
                print("✅ Material properly configured by operator")
                return True
            else:
                print("❌ Material not properly configured by operator")
                return False
        else:
            print("❌ Operator function failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing operator workflow: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_shader_matcap_uniforms():
    """Test if the shader is receiving matcap uniforms correctly"""
    print("\n=== TESTING SHADER MATCAP UNIFORMS ===")
    
    try:
        from .shaders import SDFRenderer
        
        if not SDFRenderer._shader:
            print("❌ No shader available")
            return False
        
        # Set up a known matcap state
        scene = bpy.context.scene
        mat = scene.sdf.material
        
        mat.use_matcap = True
        mat.shading_mode = '1'
        mat.matcap_intensity = 0.8
        mat.base_color = (1.0, 0.5, 0.0, 1.0)  # Orange base
        
        # Create a matcap texture
        if not SDFRenderer._matcap_texture:
            SDFRenderer.create_default_matcap()
        
        print("Testing uniform setting with known values...")
        
        # Test the uniform setting function
        SDFRenderer._set_matcap_uniforms(scene)
        
        print("✅ Uniforms set without error")
        
        # The shader should now have the correct uniforms
        # We can't easily read them back, but we can check if the function completed
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing shader uniforms: {e}")
        import traceback
        traceback.print_exc()
        return False


def comprehensive_matcap_test():
    """Run a comprehensive test of the matcap system"""
    print("\n=== COMPREHENSIVE MATCAP TEST ===")
    
    try:
        # Step 1: Ensure viewport rendering is enabled
        scene = bpy.context.scene
        scene.sdf.sdf_show_in_viewport = True
        print("✅ Enabled viewport rendering")
        
        # Step 2: Create or find a test image
        test_image_name = "comprehensive_test_matcap.exr"
        if test_image_name not in bpy.data.images:
            test_image = bpy.data.images.new(test_image_name, 256, 256)
            test_image.generated_type = 'UV_GRID'
            test_image.pack()
            print(f"✅ Created test image: {test_image_name}")
        else:
            print(f"✅ Using existing image: {test_image_name}")
        
        # Step 3: Load as matcap
        from .shaders import SDFRenderer
        if SDFRenderer.load_matcap_from_blender_image(test_image_name):
            print("✅ Loaded test image as matcap")
        else:
            print("❌ Failed to load test image as matcap")
            return False
        
        # Step 4: Configure material
        mat = scene.sdf.material
        mat.matcap_image = test_image_name
        mat.use_matcap = True
        mat.shading_mode = '1'  # Matcap mode
        mat.matcap_intensity = 1.0
        mat.base_color = (1.0, 1.0, 1.0, 1.0)
        print("✅ Configured material properties")
        
        # Step 5: Refresh shader
        SDFRenderer.refresh_shader()
        print("✅ Refreshed shader")
        
        # Step 6: Test uniform setting
        SDFRenderer._set_matcap_uniforms(scene)
        print("✅ Set shader uniforms")
        
        # Step 7: Final state check
        print("\nFinal matcap state:")
        print(f"  Viewport rendering: {scene.sdf.sdf_show_in_viewport}")
        print(f"  Renderer enabled: {SDFRenderer.is_enabled()}")
        print(f"  Has shader: {SDFRenderer._shader is not None}")
        print(f"  Has matcap texture: {SDFRenderer._matcap_texture is not None}")
        print(f"  Use matcap: {mat.use_matcap}")
        print(f"  Shading mode: {mat.shading_mode}")
        print(f"  Matcap intensity: {mat.matcap_intensity}")
        print(f"  Matcap image: {mat.matcap_image}")
        
        print("\n✅ COMPREHENSIVE TEST COMPLETE")
        print("If matcap still doesn't show in viewport, the issue is likely:")
        print("1. Shader compilation or uniform binding")
        print("2. Texture format or binding")
        print("3. Fragment shader matcap calculation")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in comprehensive test: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_exr_matcap_tests():
    """Run all EXR matcap tests"""
    print("=" * 60)
    print("EXR MATCAP WORKFLOW TESTING")
    print("=" * 60)
    
    tests = [
        ("EXR Loading Simulation", simulate_exr_loading),
        ("Matcap Operator Workflow", test_matcap_operator_workflow),
        ("Shader Matcap Uniforms", test_shader_matcap_uniforms),
        ("Comprehensive Matcap Test", comprehensive_matcap_test),
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
    
    print("\n" + "=" * 60)
    print("EXR MATCAP TESTING COMPLETE")
    print("=" * 60)


if __name__ == "__main__":
    run_exr_matcap_tests()
