"""
<PERSON><PERSON><PERSON> to create default matcap textures for the SDF addon.
This script can be run in Blender to generate basic matcap images.
"""

import bpy
import bmesh
import numpy as np
from mathutils import Vector


def create_matcap_image(name, size=256, light_dir=(0.5, 0.5, 0.7), base_color=(0.8, 0.8, 0.9)):
    """Create a basic matcap image with spherical lighting"""
    
    # Remove existing image if it exists
    if name in bpy.data.images:
        bpy.data.images.remove(bpy.data.images[name])
    
    # Create new image
    image = bpy.data.images.new(name, size, size, alpha=True)
    
    # Create pixel data
    pixels = np.zeros((size, size, 4), dtype=np.float32)
    
    center = size // 2
    light_dir = np.array(light_dir)
    light_dir = light_dir / np.linalg.norm(light_dir)
    
    for y in range(size):
        for x in range(size):
            # Calculate distance from center
            dx = (x - center) / center
            dy = (y - center) / center
            dist = np.sqrt(dx*dx + dy*dy)
            
            if dist <= 1.0:
                # Calculate surface normal for sphere
                z = np.sqrt(max(0, 1.0 - dist*dist))
                normal = np.array([dx, dy, z])
                normal = normal / np.linalg.norm(normal)
                
                # Calculate lighting
                diffuse = max(0.1, np.dot(normal, light_dir))
                
                # Add some rim lighting
                view_dir = np.array([0, 0, 1])
                rim = 1.0 - abs(np.dot(normal, view_dir))
                rim = pow(rim, 2) * 0.3
                
                # Combine lighting
                final_intensity = diffuse + rim
                final_intensity = min(1.0, final_intensity)
                
                # Apply base color
                color = np.array(base_color) * final_intensity
                pixels[y, x] = [color[0], color[1], color[2], 1.0]
            else:
                # Outside sphere - transparent
                pixels[y, x] = [0.0, 0.0, 0.0, 0.0]
    
    # Set image pixels
    image.pixels = pixels.flatten()
    image.pack()
    
    print(f"Created matcap: {name}")
    return image


def create_default_matcaps():
    """Create a set of default matcap images"""
    
    matcaps = [
        {
            'name': 'SDF_Matcap_Clay',
            'light_dir': (0.5, 0.5, 0.7),
            'base_color': (0.9, 0.85, 0.8),
        },
        {
            'name': 'SDF_Matcap_Metal',
            'light_dir': (0.3, 0.7, 0.6),
            'base_color': (0.7, 0.75, 0.8),
        },
        {
            'name': 'SDF_Matcap_Warm',
            'light_dir': (0.6, 0.4, 0.7),
            'base_color': (1.0, 0.9, 0.7),
        },
        {
            'name': 'SDF_Matcap_Cool',
            'light_dir': (0.4, 0.6, 0.7),
            'base_color': (0.7, 0.8, 1.0),
        },
        {
            'name': 'SDF_Matcap_Bright',
            'light_dir': (0.0, 0.0, 1.0),
            'base_color': (0.95, 0.95, 0.95),
        }
    ]
    
    created_images = []
    for matcap_info in matcaps:
        image = create_matcap_image(**matcap_info)
        created_images.append(image)
    
    print(f"Created {len(created_images)} default matcaps")
    return created_images


def create_advanced_matcap(name, size=256):
    """Create a more advanced matcap with multiple light sources"""
    
    # Remove existing image if it exists
    if name in bpy.data.images:
        bpy.data.images.remove(bpy.data.images[name])
    
    # Create new image
    image = bpy.data.images.new(name, size, size, alpha=True)
    
    # Create pixel data
    pixels = np.zeros((size, size, 4), dtype=np.float32)
    
    center = size // 2
    
    # Multiple light sources
    lights = [
        {'dir': np.array([0.5, 0.5, 0.7]), 'color': np.array([1.0, 1.0, 1.0]), 'intensity': 0.8},
        {'dir': np.array([-0.3, 0.2, 0.5]), 'color': np.array([0.8, 0.9, 1.0]), 'intensity': 0.3},
        {'dir': np.array([0.0, -0.8, 0.3]), 'color': np.array([1.0, 0.9, 0.8]), 'intensity': 0.2},
    ]
    
    # Normalize light directions
    for light in lights:
        light['dir'] = light['dir'] / np.linalg.norm(light['dir'])
    
    for y in range(size):
        for x in range(size):
            # Calculate distance from center
            dx = (x - center) / center
            dy = (y - center) / center
            dist = np.sqrt(dx*dx + dy*dy)
            
            if dist <= 1.0:
                # Calculate surface normal for sphere
                z = np.sqrt(max(0, 1.0 - dist*dist))
                normal = np.array([dx, dy, z])
                normal = normal / np.linalg.norm(normal)
                
                # Accumulate lighting from all sources
                final_color = np.array([0.0, 0.0, 0.0])
                
                for light in lights:
                    # Diffuse lighting
                    diffuse = max(0.0, np.dot(normal, light['dir']))
                    
                    # Add specular highlight
                    view_dir = np.array([0, 0, 1])
                    reflect_dir = 2 * np.dot(normal, light['dir']) * normal - light['dir']
                    specular = max(0.0, np.dot(reflect_dir, view_dir))
                    specular = pow(specular, 32) * 0.5
                    
                    # Combine diffuse and specular
                    light_contribution = (diffuse + specular) * light['intensity']
                    final_color += light_contribution * light['color']
                
                # Add ambient lighting
                ambient = 0.1
                final_color += ambient
                
                # Clamp to valid range
                final_color = np.clip(final_color, 0.0, 1.0)
                
                pixels[y, x] = [final_color[0], final_color[1], final_color[2], 1.0]
            else:
                # Outside sphere - transparent
                pixels[y, x] = [0.0, 0.0, 0.0, 0.0]
    
    # Set image pixels
    image.pixels = pixels.flatten()
    image.pack()
    
    print(f"Created advanced matcap: {name}")
    return image


if __name__ == "__main__":
    # Create default matcaps when run as script
    print("Creating default matcaps...")
    create_default_matcaps()
    create_advanced_matcap("SDF_Matcap_Advanced")
    print("Done!")
