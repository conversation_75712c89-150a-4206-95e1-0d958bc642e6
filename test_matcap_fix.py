"""
Test script to verify matcap fixes for Blender 4.2+
Run this to test if the matcap system is working properly.
"""

import bpy


def test_matcap_texture_binding():
    """Test if texture binding works with the new API"""
    print("\n=== Testing Matcap Texture Binding ===")
    
    try:
        from .shaders import SDFRenderer
        
        # Create a default matcap first
        print("Creating default matcap...")
        if not SDFRenderer.create_default_matcap():
            print("❌ Failed to create default matcap")
            return False
        
        print("✅ Default matcap created")
        
        # Test texture binding
        if SDFRenderer._matcap_texture:
            print("Testing texture binding...")
            try:
                # Try new API
                SDFRenderer._matcap_texture.bind(0)
                print("✅ New texture binding API works")
                return True
            except AttributeError:
                print("⚠️  New API not available, checking old API...")
                try:
                    import gpu
                    gpu.state.active_texture_set(SDFRenderer._matcap_texture, 0)
                    print("✅ Old texture binding API works")
                    return True
                except AttributeError:
                    print("❌ No texture binding API available")
                    return False
        else:
            print("❌ No matcap texture available for binding test")
            return False
            
    except Exception as e:
        print(f"❌ Error in texture binding test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_viewport_redraw():
    """Test if viewport redraw works"""
    print("\n=== Testing Viewport Redraw ===")
    
    try:
        from .shaders import SDFRenderer
        
        print("Testing viewport redraw...")
        SDFRenderer._force_viewport_redraw()
        print("✅ Viewport redraw completed without errors")
        return True
        
    except Exception as e:
        print(f"❌ Error in viewport redraw test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_matcap_rendering():
    """Test complete matcap rendering pipeline"""
    print("\n=== Testing Complete Matcap Rendering ===")
    
    try:
        scene = bpy.context.scene
        
        # Check if properties exist
        if not hasattr(scene, 'sdf') or not hasattr(scene.sdf, 'material'):
            print("❌ SDF material properties not found")
            return False
        
        mat = scene.sdf.material
        
        # Store original settings
        original_use_matcap = mat.use_matcap
        original_mode = mat.shading_mode
        original_intensity = mat.matcap_intensity
        
        try:
            # Enable matcap
            print("Enabling matcap...")
            mat.use_matcap = True
            mat.shading_mode = '1'  # Matcap mode
            mat.matcap_intensity = 0.8
            
            # Create default matcap
            from .shaders import SDFRenderer
            if not SDFRenderer.create_default_matcap():
                print("❌ Failed to create matcap for rendering test")
                return False
            
            # Test uniform setting
            print("Testing uniform setting...")
            SDFRenderer._set_matcap_uniforms(scene)
            print("✅ Matcap uniforms set successfully")
            
            # Test if renderer is working
            if SDFRenderer.is_enabled():
                print("✅ Renderer is enabled")
            else:
                print("⚠️  Renderer is not enabled (may be normal)")
            
            return True
            
        finally:
            # Restore original settings
            mat.use_matcap = original_use_matcap
            mat.shading_mode = original_mode
            mat.matcap_intensity = original_intensity
            
    except Exception as e:
        print(f"❌ Error in matcap rendering test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_operator_availability():
    """Test if matcap operators are available"""
    print("\n=== Testing Operator Availability ===")
    
    try:
        operators_to_check = [
            'load_default_matcap',
            'load_matcap_file',
            'load_matcap_from_blender',
            'load_viewport_matcap'
        ]
        
        available_count = 0
        for op_name in operators_to_check:
            if hasattr(bpy.ops.sdf, op_name):
                print(f"✅ {op_name} operator available")
                available_count += 1
            else:
                print(f"❌ {op_name} operator missing")
        
        if available_count == len(operators_to_check):
            print("✅ All matcap operators are available")
            return True
        else:
            print(f"⚠️  {available_count}/{len(operators_to_check)} operators available")
            return available_count > 0
            
    except Exception as e:
        print(f"❌ Error checking operators: {e}")
        return False


def test_default_matcap_operator():
    """Test the default matcap operator"""
    print("\n=== Testing Default Matcap Operator ===")
    
    try:
        if not hasattr(bpy.ops.sdf, 'load_default_matcap'):
            print("❌ load_default_matcap operator not available")
            return False
        
        print("Executing default matcap operator...")
        result = bpy.ops.sdf.load_default_matcap()
        
        if result == {'FINISHED'}:
            print("✅ Default matcap operator executed successfully")
            
            # Check if matcap was created
            if "SDF_Default_Matcap" in bpy.data.images:
                print("✅ Default matcap image created")
                return True
            else:
                print("⚠️  Operator finished but no image found")
                return False
        else:
            print(f"❌ Operator returned: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing default matcap operator: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_matcap_fix_tests():
    """Run all matcap fix tests"""
    print("=" * 60)
    print("TESTING MATCAP FIXES FOR BLENDER 4.2+")
    print("=" * 60)
    
    tests = [
        ("Texture Binding", test_matcap_texture_binding),
        ("Viewport Redraw", test_viewport_redraw),
        ("Operator Availability", test_operator_availability),
        ("Default Matcap Operator", test_default_matcap_operator),
        ("Complete Matcap Rendering", test_matcap_rendering),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\nRunning: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("MATCAP FIX TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("🎉 All matcap fix tests passed! Matcaps should be working now.")
    elif passed > 0:
        print("⚠️  Some tests passed. Matcaps may work partially.")
    else:
        print("❌ All tests failed. Matcaps are not working.")
    
    return passed >= len(results) // 2  # Consider success if at least half pass


if __name__ == "__main__":
    run_matcap_fix_tests()
