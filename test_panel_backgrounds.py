"""
Test script for panel background functionality.
This script demonstrates how to set up background images on panels.
"""

import bpy
import os


def create_test_background_image():
    """Create a test background image"""
    print("\n=== Creating Test Background Image ===")
    
    try:
        # Create a test image with a gradient
        image_name = "SDF_Panel_Background_Test"
        
        # Remove existing image if it exists
        if image_name in bpy.data.images:
            bpy.data.images.remove(bpy.data.images[image_name])
        
        # Create new image
        image = bpy.data.images.new(image_name, 512, 512, alpha=True)
        
        # Create a simple gradient pattern
        pixels = []
        for y in range(512):
            for x in range(512):
                # Create a radial gradient
                center_x, center_y = 256, 256
                distance = ((x - center_x) ** 2 + (y - center_y) ** 2) ** 0.5
                normalized_distance = min(distance / 256, 1.0)
                
                # Create a nice blue-to-purple gradient
                r = 0.2 + 0.3 * normalized_distance
                g = 0.3 + 0.2 * (1.0 - normalized_distance)
                b = 0.8 - 0.3 * normalized_distance
                a = 0.8  # Semi-transparent
                
                pixels.extend([r, g, b, a])
        
        # Set image pixels
        image.pixels = pixels
        image.pack()
        
        print(f"✅ Created test background image: {image_name}")
        return image_name
        
    except Exception as e:
        print(f"❌ Error creating test background image: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_panel_background_manager():
    """Test the panel background manager"""
    print("\n=== Testing Panel Background Manager ===")
    
    try:
        from .panel_backgrounds import PanelBackgroundManager
        
        # Initialize the manager
        if PanelBackgroundManager.initialize():
            print("✅ Panel Background Manager initialized")
        else:
            print("❌ Failed to initialize Panel Background Manager")
            return False
        
        # Create a test image
        test_image = create_test_background_image()
        if not test_image:
            print("❌ Failed to create test image")
            return False
        
        # Set background for main panel
        if PanelBackgroundManager.set_panel_background(
            'SDF_PT_tree_panel',
            test_image,
            alpha=0.25,
            tint_color=(1.0, 1.0, 1.0)
        ):
            print("✅ Set background for main tree panel")
        else:
            print("❌ Failed to set background for main tree panel")
            return False
        
        # Set background for settings panel with different settings
        if PanelBackgroundManager.set_panel_background(
            'SDF_PT_tree_settings',
            test_image,
            alpha=0.15,
            tint_color=(0.8, 1.0, 0.8)  # Slight green tint
        ):
            print("✅ Set background for settings panel")
        else:
            print("❌ Failed to set background for settings panel")
            return False
        
        print("✅ Panel Background Manager test completed")
        return True
        
    except Exception as e:
        print(f"❌ Error testing Panel Background Manager: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_panel_background_operator():
    """Test the panel background operator"""
    print("\n=== Testing Panel Background Operator ===")
    
    try:
        # Check if operator exists
        if not hasattr(bpy.ops.sdf, 'set_panel_background'):
            print("❌ set_panel_background operator not found")
            return False
        
        print("✅ Panel background operator found")
        
        # Create test image if it doesn't exist
        test_image = "SDF_Panel_Background_Test"
        if test_image not in bpy.data.images:
            test_image = create_test_background_image()
            if not test_image:
                return False
        
        # Test operator execution (without dialog)
        # Note: We can't easily test the dialog, but we can test the underlying functionality
        print("✅ Panel background operator is available")
        return True
        
    except Exception as e:
        print(f"❌ Error testing panel background operator: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_panel_mixin():
    """Test the panel mixin functionality"""
    print("\n=== Testing Panel Mixin ===")
    
    try:
        from .panel_backgrounds import PanelBackgroundMixin
        
        # Check if the mixin class exists
        print("✅ PanelBackgroundMixin class found")
        
        # The actual drawing test would require a full UI context
        # which is difficult to simulate in a test script
        print("✅ Panel mixin test completed (drawing requires UI context)")
        return True
        
    except Exception as e:
        print(f"❌ Error testing panel mixin: {e}")
        import traceback
        traceback.print_exc()
        return False


def demonstrate_panel_backgrounds():
    """Demonstrate setting up panel backgrounds"""
    print("\n=== DEMONSTRATING PANEL BACKGROUNDS ===")
    
    try:
        # Create a nice background image
        print("Creating demonstration background...")
        
        image_name = "SDF_Demo_Background"
        if image_name in bpy.data.images:
            bpy.data.images.remove(bpy.data.images[image_name])
        
        # Create a more sophisticated background
        image = bpy.data.images.new(image_name, 1024, 1024, alpha=True)
        
        pixels = []
        for y in range(1024):
            for x in range(1024):
                # Create a circuit board pattern
                grid_x = x // 64
                grid_y = y // 64
                
                # Base color
                r, g, b = 0.1, 0.15, 0.3
                
                # Add grid lines
                if x % 64 < 2 or y % 64 < 2:
                    r, g, b = 0.2, 0.4, 0.6
                
                # Add some random "components"
                if (grid_x + grid_y) % 3 == 0 and x % 64 > 20 and x % 64 < 44 and y % 64 > 20 and y % 64 < 44:
                    r, g, b = 0.3, 0.6, 0.9
                
                pixels.extend([r, g, b, 0.7])  # Semi-transparent
        
        image.pixels = pixels
        image.pack()
        
        print(f"✅ Created demonstration background: {image_name}")
        
        # Set up backgrounds using the manager
        from .panel_backgrounds import PanelBackgroundManager
        
        # Initialize if not already done
        PanelBackgroundManager.initialize()
        
        # Set background for main panel
        PanelBackgroundManager.set_panel_background(
            'SDF_PT_tree_panel',
            image_name,
            alpha=0.25,
            tint_color=(1.0, 1.0, 1.0),
            scale=(1.0, 1.0)
        )
        
        # Set background for settings panel with different opacity
        PanelBackgroundManager.set_panel_background(
            'SDF_PT_tree_settings',
            image_name,
            alpha=0.15,
            tint_color=(0.9, 1.0, 1.0),  # Slight cyan tint
            scale=(0.8, 0.8)
        )
        
        print("✅ Panel backgrounds configured!")
        print("Check the SDF panels in the 3D viewport sidebar to see the backgrounds")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in demonstration: {e}")
        import traceback
        traceback.print_exc()
        return False


def remove_panel_backgrounds():
    """Remove all panel backgrounds"""
    print("\n=== REMOVING PANEL BACKGROUNDS ===")
    
    try:
        from .panel_backgrounds import PanelBackgroundManager
        
        PanelBackgroundManager.remove_panel_background('SDF_PT_tree_panel')
        PanelBackgroundManager.remove_panel_background('SDF_PT_tree_settings')
        
        print("✅ Panel backgrounds removed")
        return True
        
    except Exception as e:
        print(f"❌ Error removing backgrounds: {e}")
        return False


def run_panel_background_tests():
    """Run all panel background tests"""
    print("=" * 60)
    print("PANEL BACKGROUND SYSTEM TESTING")
    print("=" * 60)
    
    tests = [
        ("Panel Background Manager", test_panel_background_manager),
        ("Panel Background Operator", test_panel_background_operator),
        ("Panel Mixin", test_panel_mixin),
        ("Demonstration Setup", demonstrate_panel_backgrounds),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("PANEL BACKGROUND TEST RESULTS")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
    
    print(f"\nPassed: {passed}/{total} tests")
    
    if passed == total:
        print("🎉 All panel background tests passed!")
        print("\nTo use panel backgrounds:")
        print("1. Go to the SDF Tree Builder panel")
        print("2. Click 'Set Background Image' in the Panel Backgrounds section")
        print("3. Select a panel and image, adjust transparency")
        print("4. The background will appear behind the panel content")
    else:
        print("⚠️  Some tests failed. Check the output above.")
    
    return passed == total


if __name__ == "__main__":
    run_panel_background_tests()
