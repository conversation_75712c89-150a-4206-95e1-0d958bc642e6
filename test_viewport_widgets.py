"""
Test and demo script for SDF viewport widgets.
Run this in Blender's Text Editor to test the interactive transform widgets.
"""

import bpy

def test_widget_setup():
    """Test that viewport widgets can be enabled"""
    print("🧪 Testing Viewport Widget Setup...")
    
    # Check if the operator exists
    try:
        bpy.ops.sdf.toggle_viewport_widgets()
        print("✅ Viewport widgets operator found and executed")
        return True
    except Exception as e:
        print(f"❌ Viewport widgets operator failed: {e}")
        return False

def create_widget_demo_scene():
    """Create a demo scene for testing viewport widgets"""
    print("🎬 Creating Viewport Widget Demo Scene...")
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ SDF tree not found")
        return False
    
    tree = scene.sdf_tree
    
    # Clear tree
    tree.items.clear()
    tree.active_index = 0
    
    # Create a simple hierarchy for testing
    print("Creating test hierarchy...")
    
    # Main group
    bpy.ops.sdf.tree_add_group()
    main_group = tree.items[0]
    main_group.name = "Widget Test Group"
    main_group.location = (0.0, 0.0, 0.0)
    
    # Add a sphere child
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='SPHERE')
    sphere = tree.items[1]
    sphere.name = "Test Sphere"
    sphere.location = (1.0, 0.0, 0.0)
    sphere.radius = 0.8
    
    # Add a box child
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='BOX')
    box = tree.items[2]
    box.name = "Test Box"
    box.location = (-1.0, 0.0, 0.0)
    box.size = (0.6, 0.6, 0.6)
    box.boolean_mode = 'UNION'
    
    # Add a cylinder at root level
    bpy.ops.sdf.tree_add_cylinder()
    cylinder = tree.items[3]
    cylinder.name = "Test Cylinder"
    cylinder.location = (0.0, 2.0, 0.0)
    cylinder.radius = 0.4
    cylinder.height = 1.5
    cylinder.boolean_mode = 'SUBTRACT'
    
    print("✅ Demo scene created!")
    print("\nHierarchy:")
    for i, item in enumerate(tree.items):
        indent = "  " * item.indent_level
        parent_info = f"(parent: {item.parent_index})" if item.parent_index >= 0 else "(root)"
        print(f"{i}: {indent}{item.name} {parent_info}")
        print(f"    {indent}Location: {tuple(item.location)}")
        if item.item_type != 'GROUP':
            world_loc = item.get_world_location(tree)
            print(f"    {indent}World: {world_loc}")
    
    # Select the main group for widget testing
    tree.active_index = 0
    
    return True

def test_widget_interaction():
    """Test widget interaction functionality"""
    print("\n🧪 Testing Widget Interaction...")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    if not tree.items:
        print("❌ No items in tree for testing")
        return False
    
    # Test getting active item
    from .sdf_viewport_widgets import get_active_sdf_item
    
    item, tree_ref = get_active_sdf_item()
    if item:
        print(f"✅ Active item found: {item.name}")
        print(f"   Location: {tuple(item.location)}")
        print(f"   World Location: {item.get_world_location(tree_ref)}")
        return True
    else:
        print("❌ No active item found")
        return False

def demo_widget_usage():
    """Demonstrate how to use the viewport widgets"""
    print("\n🎯 Viewport Widget Usage Demo")
    print("=" * 50)
    
    success1 = create_widget_demo_scene()
    if not success1:
        return False
    
    success2 = test_widget_setup()
    if not success2:
        return False
    
    success3 = test_widget_interaction()
    if not success3:
        return False
    
    print("\n🎉 Widget demo setup complete!")
    print("\n📋 How to use the viewport widgets:")
    print("1. ✅ Demo scene created with test objects")
    print("2. ✅ Viewport widgets should be enabled")
    print("3. 🎯 Select different items in the SDF tree")
    print("4. 🖱️  Look for transform widgets in the 3D viewport")
    print("5. 🎮 Try dragging the widget handles to move objects")
    print("6. 👀 Watch how children move with their parents!")
    
    print(f"\n🔧 Manual steps to test:")
    print(f"• Select 'Widget Test Group' in the tree")
    print(f"• Look for red/green/blue arrows in the viewport")
    print(f"• Try dragging the arrows to move the group")
    print(f"• Notice how all children move together")
    print(f"• Select individual children and move them")
    print(f"• Use 'Toggle Transform Widgets' button to enable/disable")
    
    return True

def test_widget_performance():
    """Test widget performance with multiple items"""
    print("\n⚡ Testing Widget Performance...")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Clear and create many items
    tree.items.clear()
    tree.active_index = 0
    
    print("Creating performance test scene with multiple items...")
    
    # Create multiple groups with children
    for i in range(3):
        # Create group
        bpy.ops.sdf.tree_add_group()
        group = tree.items[-1]
        group.name = f"Group {i+1}"
        group.location = (i * 3.0, 0.0, 0.0)
        
        # Add children to group
        group_index = len(tree.items) - 1
        tree.active_index = group_index
        
        for j in range(3):
            if j == 0:
                bpy.ops.sdf.tree_add_as_child(item_type='SPHERE')
                child = tree.items[-1]
                child.radius = 0.3
            elif j == 1:
                bpy.ops.sdf.tree_add_as_child(item_type='BOX')
                child = tree.items[-1]
                child.size = (0.4, 0.4, 0.4)
            else:
                bpy.ops.sdf.tree_add_as_child(item_type='CYLINDER')
                child = tree.items[-1]
                child.radius = 0.2
                child.height = 0.8
            
            child.name = f"Child {j+1}"
            child.location = (j * 0.8 - 0.8, 0.0, 0.0)
            child.boolean_mode = 'UNION'
    
    print(f"✅ Created {len(tree.items)} items for performance testing")
    
    # Test widget activation with many items
    try:
        bpy.ops.sdf.toggle_viewport_widgets()
        print("✅ Widgets activated with multiple items")
        
        # Test selecting different items
        for i in range(min(5, len(tree.items))):
            tree.active_index = i
            item = tree.items[i]
            print(f"   Selected: {item.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def run_all_widget_tests():
    """Run all viewport widget tests"""
    print("🚀 Starting Viewport Widget Tests")
    print("=" * 60)
    
    tests = [
        demo_widget_usage,
        test_widget_performance,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ Test passed\n")
            else:
                print("❌ Test failed\n")
        except Exception as e:
            print(f"❌ Test error: {e}\n")
            import traceback
            traceback.print_exc()
    
    print("=" * 60)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All viewport widget tests passed!")
        print("\n🎮 Interactive transform widgets are ready!")
        print("Use the 'Toggle Transform Widgets' button in the SDF panel")
        return True
    else:
        print("⚠️  Some tests failed. Check the implementation.")
        return False

if __name__ == "__main__":
    run_all_widget_tests()
