#!/usr/bin/env python3
"""
Test script for the FIXED Chamfer and NEW Fillet features.
This tests that chamfer now works correctly (no splitting) and fillet is implemented.

IMPORTANT: Reload the addon first for the new fillet property to be available!
"""

import bpy

def clear_tree():
    """Clear the SDF tree"""
    scene = bpy.context.scene
    if hasattr(scene, 'sdf_tree'):
        tree = scene.sdf_tree
        tree.items.clear()
        tree.active_index = 0
        print("✅ Tree cleared")
        return True
    else:
        print("❌ No sdf_tree found!")
        return False

def test_fillet_property():
    """Test if the fillet property exists"""
    print("\n=== TESTING FILLET PROPERTY ===")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Add a box to test properties
    bpy.ops.sdf.tree_add_box()
    
    if not tree.items:
        print("❌ No items added!")
        return False
    
    box = tree.items[-1]
    print(f"Testing properties on: {box.name}")
    
    # Check if fillet_radius property exists
    if hasattr(box, 'fillet_radius'):
        print(f"✅ fillet_radius property exists: {box.fillet_radius}")
        
        # Try to change it
        box.fillet_radius = 0.2
        print(f"✅ Set fillet_radius to: {box.fillet_radius}")
        return True
    else:
        print("❌ fillet_radius property does NOT exist!")
        print("   The addon needs to be reloaded!")
        return False

def test_fixed_chamfer():
    """Test that chamfer now works correctly (no splitting)"""
    print("\n=== TESTING FIXED CHAMFER ===")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Add a box with chamfer
    bpy.ops.sdf.tree_add_box()
    box = tree.items[-1]
    box.name = "Chamfered Box"
    box.size = (1.0, 1.0, 1.0)
    box.location = (0.0, 0.0, 0.0)
    box.chamfer_size = 0.2  # This should create 45-degree cuts, not split the cube
    
    print(f"✅ Created chamfered box:")
    print(f"   Size: {box.size[:]}")
    print(f"   Chamfer Size: {box.chamfer_size}")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        print("✅ GLSL generated:")
        print("---")
        print(glsl_code)
        print("---")
        
        if "sdBoxChamfered" in glsl_code:
            print("✅ GLSL uses fixed chamfered box function!")
            print("💡 The cube should have 45-degree cuts on edges, not be split into pieces")
            return True
        else:
            print("❌ GLSL does NOT use chamfered box function!")
            return False
            
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False

def test_fillet_functionality():
    """Test fillet functionality"""
    print("\n=== TESTING FILLET FUNCTIONALITY ===")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Add a box with fillet
    bpy.ops.sdf.tree_add_box()
    box = tree.items[-1]
    box.name = "Filleted Box"
    box.size = (1.0, 1.0, 1.0)
    box.location = (0.0, 0.0, 0.0)
    box.fillet_radius = 0.15  # This should create rounded internal corners
    
    print(f"✅ Created filleted box:")
    print(f"   Size: {box.size[:]}")
    print(f"   Fillet Radius: {box.fillet_radius}")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        print("✅ GLSL generated:")
        print("---")
        print(glsl_code)
        print("---")
        
        if "sdBoxFilleted" in glsl_code:
            print("✅ GLSL uses filleted box function!")
            print("💡 The cube should have rounded internal corners")
            return True
        else:
            print("❌ GLSL does NOT use filleted box function!")
            return False
            
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False

def create_comparison_demo():
    """Create a side-by-side comparison of bevel, chamfer, and fillet"""
    print("\n=== CREATING COMPARISON DEMO ===")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Normal box (far left)
    bpy.ops.sdf.tree_add_box()
    normal_box = tree.items[-1]
    normal_box.name = "Normal Box"
    normal_box.size = (0.8, 0.8, 0.8)
    normal_box.location = (-3.0, 0.0, 0.0)
    normal_box.boolean_mode = 'UNION'
    
    # Beveled box (left)
    bpy.ops.sdf.tree_add_box()
    beveled_box = tree.items[-1]
    beveled_box.name = "Beveled Box"
    beveled_box.size = (0.8, 0.8, 0.8)
    beveled_box.location = (-1.0, 0.0, 0.0)
    beveled_box.bevel_radius = 0.15
    beveled_box.boolean_mode = 'UNION'
    
    # Chamfered box (right)
    bpy.ops.sdf.tree_add_box()
    chamfered_box = tree.items[-1]
    chamfered_box.name = "Chamfered Box"
    chamfered_box.size = (0.8, 0.8, 0.8)
    chamfered_box.location = (1.0, 0.0, 0.0)
    chamfered_box.chamfer_size = 0.15
    chamfered_box.boolean_mode = 'UNION'
    
    # Filleted box (far right)
    bpy.ops.sdf.tree_add_box()
    filleted_box = tree.items[-1]
    filleted_box.name = "Filleted Box"
    filleted_box.size = (0.8, 0.8, 0.8)
    filleted_box.location = (3.0, 0.0, 0.0)
    filleted_box.fillet_radius = 0.15
    filleted_box.boolean_mode = 'UNION'
    
    print("✅ Created comparison demo:")
    print(f"  Far Left: {normal_box.name} - Sharp edges")
    print(f"  Left:     {beveled_box.name} - Rounded external edges (bevel: {beveled_box.bevel_radius})")
    print(f"  Right:    {chamfered_box.name} - 45-degree cuts (chamfer: {chamfered_box.chamfer_size})")
    print(f"  Far Right:{filleted_box.name} - Rounded internal corners (fillet: {filleted_box.fillet_radius})")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        print("\n✅ Comparison GLSL generated successfully")
        
        # Check for all expected functions
        checks = [
            ("sdBox(", "Normal box"),
            ("sdBoxBeveled", "Beveled box"),
            ("sdBoxChamfered", "Chamfered box"),
            ("sdBoxFilleted", "Filleted box"),
        ]
        
        all_good = True
        for check_str, description in checks:
            if check_str in glsl_code:
                print(f"✅ {description} found")
            else:
                print(f"❌ {description} NOT found")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Comparison GLSL generation failed: {e}")
        return False

def test_priority_system():
    """Test the priority system: Bevel > Chamfer > Fillet > Normal"""
    print("\n=== TESTING PRIORITY SYSTEM ===")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Test: Box with all three properties set
    bpy.ops.sdf.tree_add_box()
    box = tree.items[-1]
    box.name = "All Properties Set"
    box.bevel_radius = 0.1
    box.chamfer_size = 0.2
    box.fillet_radius = 0.3
    
    print(f"Box with all properties:")
    print(f"  Bevel: {box.bevel_radius}")
    print(f"  Chamfer: {box.chamfer_size}")
    print(f"  Fillet: {box.fillet_radius}")
    
    try:
        glsl_code = tree.generate_glsl()
        
        if "sdBoxBeveled" in glsl_code:
            print("✅ Bevel takes priority (correct)")
            
            # Now remove bevel and test chamfer priority
            box.bevel_radius = 0.0
            glsl_code = tree.generate_glsl()
            
            if "sdBoxChamfered" in glsl_code:
                print("✅ Chamfer takes priority when bevel is 0 (correct)")
                
                # Now remove chamfer and test fillet
                box.chamfer_size = 0.0
                glsl_code = tree.generate_glsl()
                
                if "sdBoxFilleted" in glsl_code:
                    print("✅ Fillet takes priority when bevel and chamfer are 0 (correct)")
                    return True
                else:
                    print("❌ Fillet should take priority when others are 0")
                    return False
            else:
                print("❌ Chamfer should take priority when bevel is 0")
                return False
        else:
            print("❌ Bevel should take priority when all are set")
            return False
            
    except Exception as e:
        print(f"❌ Priority test failed: {e}")
        return False

def run_chamfer_fillet_tests():
    """Run all chamfer and fillet tests"""
    print("🔧 TESTING FIXED CHAMFER AND NEW FILLET")
    print("=" * 50)
    
    tests = [
        ("Fillet Property", test_fillet_property),
        ("Fixed Chamfer", test_fixed_chamfer),
        ("Fillet Functionality", test_fillet_functionality),
        ("Comparison Demo", create_comparison_demo),
        ("Priority System", test_priority_system),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 CHAMFER/FILLET TEST RESULTS:")
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Chamfer is fixed and fillet is working!")
        print("\n📖 UNDERSTANDING THE DIFFERENCES:")
        print("• BEVEL: Rounded external edges (convex)")
        print("  - Makes edges smooth and rounded outward")
        print("  - Good for soft, organic looks")
        print()
        print("• CHAMFER: 45-degree cuts on edges")
        print("  - Creates flat angled surfaces on edges")
        print("  - Good for mechanical, machined looks")
        print("  - Should NOT split the cube anymore!")
        print()
        print("• FILLET: Rounded internal corners (concave)")
        print("  - Makes internal corners smooth and rounded")
        print("  - Good for manufacturing constraints")
        print()
        print("• PRIORITY: Bevel > Chamfer > Fillet > Normal")
    else:
        print(f"\n⚠️ {len(results) - passed} test(s) failed.")
        print("Make sure the addon is properly reloaded!")

if __name__ == "__main__":
    run_chamfer_fillet_tests()
