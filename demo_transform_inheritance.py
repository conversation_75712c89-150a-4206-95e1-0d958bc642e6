"""
Visual demo of transform inheritance.
Run this in Blender's Text Editor to create a demo scene.
"""

import bpy

def create_transform_demo():
    """Create a visual demo of transform inheritance"""
    print("🎬 Creating Transform Inheritance Demo...")
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ SDF tree not found")
        return False
    
    tree = scene.sdf_tree
    
    # Clear tree
    tree.items.clear()
    tree.active_index = 0
    
    print("Creating demo hierarchy...")
    
    # Create main group
    bpy.ops.sdf.tree_add_group()
    main_group = tree.items[0]
    main_group.name = "Main Assembly"
    main_group.location = (0.0, 0.0, 0.0)
    
    # Add base sphere to main group
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='SPHERE')
    base_sphere = tree.items[1]
    base_sphere.name = "Base Sphere"
    base_sphere.location = (0.0, 0.0, 0.0)
    base_sphere.radius = 1.0
    base_sphere.boolean_mode = 'UNION'
    
    # Create details group
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='GROUP')
    details_group = tree.items[2]
    details_group.name = "Details Group"
    details_group.location = (0.0, 0.0, 0.0)
    details_group.boolean_mode = 'UNION'
    
    # Add detail spheres to details group
    tree.active_index = 2
    bpy.ops.sdf.tree_add_as_child(item_type='SPHERE')
    detail1 = tree.items[3]
    detail1.name = "Detail 1"
    detail1.location = (1.5, 0.0, 0.0)
    detail1.radius = 0.3
    detail1.boolean_mode = 'UNION'
    
    tree.active_index = 2
    bpy.ops.sdf.tree_add_as_child(item_type='SPHERE')
    detail2 = tree.items[4]
    detail2.name = "Detail 2"
    detail2.location = (-1.5, 0.0, 0.0)
    detail2.radius = 0.3
    detail2.boolean_mode = 'UNION'
    
    tree.active_index = 2
    bpy.ops.sdf.tree_add_as_child(item_type='SPHERE')
    detail3 = tree.items[5]
    detail3.name = "Detail 3"
    detail3.location = (0.0, 1.5, 0.0)
    detail3.radius = 0.3
    detail3.boolean_mode = 'UNION'
    
    # Create cutouts group
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='GROUP')
    cutouts_group = tree.items[6]
    cutouts_group.name = "Cutouts Group"
    cutouts_group.location = (0.0, 0.0, 0.0)
    cutouts_group.boolean_mode = 'SUBTRACT'
    
    # Add cutout cylinders
    tree.active_index = 6
    bpy.ops.sdf.tree_add_as_child(item_type='CYLINDER')
    cutout1 = tree.items[7]
    cutout1.name = "Cutout 1"
    cutout1.location = (0.0, 0.0, 0.0)
    cutout1.radius = 0.2
    cutout1.height = 3.0
    cutout1.boolean_mode = 'UNION'
    
    tree.active_index = 6
    bpy.ops.sdf.tree_add_as_child(item_type='CYLINDER')
    cutout2 = tree.items[8]
    cutout2.name = "Cutout 2"
    cutout2.location = (0.7, 0.7, 0.0)
    cutout2.radius = 0.15
    cutout2.height = 2.0
    cutout2.boolean_mode = 'UNION'
    
    print("✅ Demo hierarchy created!")
    print("\nHierarchy structure:")
    for i, item in enumerate(tree.items):
        indent = "  " * item.indent_level
        parent_info = f"(parent: {item.parent_index})" if item.parent_index >= 0 else "(root)"
        print(f"{i}: {indent}{item.name} {parent_info}")
        print(f"    {indent}Local: {tuple(item.location)}")
        if item.item_type != 'GROUP':
            world_loc = item.get_world_location(tree)
            print(f"    {indent}World: {world_loc}")
    
    print(f"\n🎯 Now try this:")
    print(f"1. Look at the viewport - you should see a complex shape")
    print(f"2. Select 'Main Assembly' in the tree")
    print(f"3. Change its Location to (3, 0, 0)")
    print(f"4. Watch how ALL children move together!")
    print(f"5. Try moving 'Details Group' - only its children should move")
    print(f"6. Try moving 'Cutouts Group' - only the cutouts should move")
    
    return True

def animate_transform_demo():
    """Create an animated demo showing transform inheritance"""
    print("\n🎬 Creating Animated Transform Demo...")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    if len(tree.items) == 0:
        print("❌ No items in tree. Run create_transform_demo() first.")
        return False
    
    # Find the main assembly (should be item 0)
    if len(tree.items) > 0:
        main_assembly = tree.items[0]
        
        print("Animating main assembly movement...")
        print("Watch the viewport as the entire assembly moves!")
        
        import time
        
        # Animate movement
        positions = [
            (0.0, 0.0, 0.0),
            (2.0, 0.0, 0.0),
            (2.0, 2.0, 0.0),
            (0.0, 2.0, 0.0),
            (0.0, 0.0, 0.0),
        ]
        
        for i, pos in enumerate(positions):
            print(f"Moving to position {i+1}: {pos}")
            main_assembly.location = pos
            
            # Force viewport update
            try:
                from ..shaders import SDFRenderer
                SDFRenderer.refresh_shader()
            except:
                pass
            
            # Update Blender
            bpy.context.view_layer.update()
            
            # Small delay (won't work in script, but shows the concept)
            # time.sleep(0.5)
        
        print("✅ Animation complete!")
        print("All children should have moved with the parent!")
        
        return True
    else:
        print("❌ No main assembly found")
        return False

def run_demo():
    """Run the complete transform inheritance demo"""
    print("🚀 Starting Transform Inheritance Demo")
    print("=" * 50)
    
    success1 = create_transform_demo()
    
    if success1:
        print("\n" + "=" * 50)
        print("🎉 Demo created successfully!")
        print("\nTo see transform inheritance in action:")
        print("1. Look at the SDF viewport")
        print("2. Select different groups in the tree")
        print("3. Modify their Location properties")
        print("4. Watch how children move with their parents!")
        
        # Optionally run animation demo
        print(f"\nTo run animation demo, call: animate_transform_demo()")
        
        return True
    else:
        print("❌ Demo creation failed")
        return False

if __name__ == "__main__":
    run_demo()
