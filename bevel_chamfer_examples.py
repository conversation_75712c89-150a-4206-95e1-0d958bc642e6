#!/usr/bin/env python3
"""
Visual examples showing the difference between <PERSON><PERSON> and <PERSON><PERSON><PERSON>.
This creates side-by-side comparisons to demonstrate the effects.
"""

import bpy

def clear_tree():
    """Clear the SDF tree"""
    scene = bpy.context.scene
    if hasattr(scene, 'sdf_tree'):
        tree = scene.sdf_tree
        tree.items.clear()
        tree.active_index = 0
        return True
    return False

def create_comparison_boxes():
    """Create a comparison of normal, beveled, and chamfered boxes"""
    print("🔧 CREATING BOX COMPARISON")
    print("=" * 40)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Normal box (left)
    bpy.ops.sdf.tree_add_box()
    normal_box = tree.items[-1]
    normal_box.name = "Normal Box"
    normal_box.size = (0.8, 0.8, 0.8)
    normal_box.location = (-2.0, 0.0, 0.0)
    normal_box.boolean_mode = 'UNION'
    
    # Beveled box (center)
    bpy.ops.sdf.tree_add_box()
    beveled_box = tree.items[-1]
    beveled_box.name = "Beveled Box"
    beveled_box.size = (0.8, 0.8, 0.8)
    beveled_box.location = (0.0, 0.0, 0.0)
    beveled_box.bevel_radius = 0.15
    beveled_box.boolean_mode = 'UNION'
    
    # Chamfered box (right)
    bpy.ops.sdf.tree_add_box()
    chamfered_box = tree.items[-1]
    chamfered_box.name = "Chamfered Box"
    chamfered_box.size = (0.8, 0.8, 0.8)
    chamfered_box.location = (2.0, 0.0, 0.0)
    chamfered_box.chamfer_size = 0.15
    chamfered_box.boolean_mode = 'UNION'
    
    print("✅ Created box comparison:")
    print(f"  Left:   {normal_box.name} - Sharp edges")
    print(f"  Center: {beveled_box.name} - Rounded edges (bevel: {beveled_box.bevel_radius})")
    print(f"  Right:  {chamfered_box.name} - Cut edges (chamfer: {chamfered_box.chamfer_size})")

def create_comparison_cylinders():
    """Create a comparison of normal, beveled, and chamfered cylinders"""
    print("\n🔧 CREATING CYLINDER COMPARISON")
    print("=" * 40)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Normal cylinder (left)
    bpy.ops.sdf.tree_add_cylinder()
    normal_cyl = tree.items[-1]
    normal_cyl.name = "Normal Cylinder"
    normal_cyl.radius = 0.6
    normal_cyl.height = 1.5
    normal_cyl.location = (-2.0, 0.0, 0.0)
    normal_cyl.boolean_mode = 'UNION'
    
    # Beveled cylinder (center)
    bpy.ops.sdf.tree_add_cylinder()
    beveled_cyl = tree.items[-1]
    beveled_cyl.name = "Beveled Cylinder"
    beveled_cyl.radius = 0.6
    beveled_cyl.height = 1.5
    beveled_cyl.location = (0.0, 0.0, 0.0)
    beveled_cyl.bevel_radius = 0.1
    beveled_cyl.boolean_mode = 'UNION'
    
    # Chamfered cylinder (right)
    bpy.ops.sdf.tree_add_cylinder()
    chamfered_cyl = tree.items[-1]
    chamfered_cyl.name = "Chamfered Cylinder"
    chamfered_cyl.radius = 0.6
    chamfered_cyl.height = 1.5
    chamfered_cyl.location = (2.0, 0.0, 0.0)
    chamfered_cyl.chamfer_size = 0.1
    chamfered_cyl.boolean_mode = 'UNION'
    
    print("✅ Created cylinder comparison:")
    print(f"  Left:   {normal_cyl.name} - Sharp edges")
    print(f"  Center: {beveled_cyl.name} - Rounded edges (bevel: {beveled_cyl.bevel_radius})")
    print(f"  Right:  {chamfered_cyl.name} - Cut edges (chamfer: {chamfered_cyl.chamfer_size})")

def create_progressive_bevel_demo():
    """Create a demo showing progressive bevel amounts"""
    print("\n🔧 CREATING PROGRESSIVE BEVEL DEMO")
    print("=" * 40)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    bevel_values = [0.0, 0.05, 0.1, 0.2, 0.3]
    
    for i, bevel_val in enumerate(bevel_values):
        bpy.ops.sdf.tree_add_box()
        box = tree.items[-1]
        box.name = f"Bevel {bevel_val}"
        box.size = (0.6, 0.6, 0.6)
        box.location = (i * 1.5 - 3.0, 0.0, 0.0)
        box.bevel_radius = bevel_val
        box.boolean_mode = 'UNION'
        
        print(f"  Box {i+1}: Bevel radius {bevel_val}")

def create_progressive_chamfer_demo():
    """Create a demo showing progressive chamfer amounts"""
    print("\n🔧 CREATING PROGRESSIVE CHAMFER DEMO")
    print("=" * 40)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    chamfer_values = [0.0, 0.05, 0.1, 0.2, 0.3]
    
    for i, chamfer_val in enumerate(chamfer_values):
        bpy.ops.sdf.tree_add_box()
        box = tree.items[-1]
        box.name = f"Chamfer {chamfer_val}"
        box.size = (0.6, 0.6, 0.6)
        box.location = (i * 1.5 - 3.0, 0.0, 0.0)
        box.chamfer_size = chamfer_val
        box.boolean_mode = 'UNION'
        
        print(f"  Box {i+1}: Chamfer size {chamfer_val}")

def create_architectural_example():
    """Create an architectural example using bevels and chamfers"""
    print("\n🏗️ CREATING ARCHITECTURAL EXAMPLE")
    print("=" * 40)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Base platform (beveled)
    bpy.ops.sdf.tree_add_box()
    base = tree.items[-1]
    base.name = "Base Platform"
    base.size = (3.0, 3.0, 0.2)
    base.location = (0.0, 0.0, -1.0)
    base.bevel_radius = 0.05
    base.boolean_mode = 'UNION'
    
    # Main building (chamfered corners)
    bpy.ops.sdf.tree_add_box()
    building = tree.items[-1]
    building.name = "Main Building"
    building.size = (2.0, 2.0, 1.5)
    building.location = (0.0, 0.0, 0.0)
    building.chamfer_size = 0.1
    building.boolean_mode = 'UNION'
    building.smooth_radius = 0.05
    
    # Tower (beveled)
    bpy.ops.sdf.tree_add_cylinder()
    tower = tree.items[-1]
    tower.name = "Tower"
    tower.radius = 0.6
    tower.height = 2.0
    tower.location = (1.2, 1.2, 1.0)
    tower.bevel_radius = 0.08
    tower.boolean_mode = 'UNION'
    tower.smooth_radius = 0.1
    
    # Windows (chamfered holes)
    for i, pos in enumerate([(-0.8, 0.0, 0.3), (0.8, 0.0, 0.3), (0.0, -0.8, 0.3)]):
        bpy.ops.sdf.tree_add_box()
        window = tree.items[-1]
        window.name = f"Window {i+1}"
        window.size = (0.3, 0.1, 0.4)
        window.location = pos
        window.chamfer_size = 0.02
        window.boolean_mode = 'SUBTRACT'
        window.smooth_radius = 0.02
    
    print("✅ Created architectural example:")
    for i, item in enumerate(tree.items):
        edge_info = ""
        if item.bevel_radius > 0:
            edge_info = f" (bevel: {item.bevel_radius})"
        elif item.chamfer_size > 0:
            edge_info = f" (chamfer: {item.chamfer_size})"
        
        print(f"  {i+1}: {item.name} - {item.boolean_mode}{edge_info}")

def run_visual_examples():
    """Run all visual examples"""
    print("🎨 BEVEL AND CHAMFER VISUAL EXAMPLES")
    print("=" * 50)
    
    examples = [
        ("Box Comparison", create_comparison_boxes),
        ("Cylinder Comparison", create_comparison_cylinders),
        ("Progressive Bevel", create_progressive_bevel_demo),
        ("Progressive Chamfer", create_progressive_chamfer_demo),
        ("Architectural Example", create_architectural_example),
    ]
    
    print("Choose an example to run:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"  {i}. {name}")
    
    try:
        choice = input("\nEnter example number (1-5) or 'all': ").strip().lower()
        
        if choice == 'all':
            for name, example_func in examples:
                print(f"\n{'='*20} {name.upper()} {'='*20}")
                example_func()
                input("Press Enter to continue to next example...")
        else:
            choice_num = int(choice) - 1
            if 0 <= choice_num < len(examples):
                name, example_func = examples[choice_num]
                print(f"\n{'='*20} {name.upper()} {'='*20}")
                example_func()
            else:
                print("Invalid choice!")
                
    except (ValueError, KeyboardInterrupt):
        print("Running all examples...")
        for name, example_func in examples:
            print(f"\n{'='*20} {name.upper()} {'='*20}")
            example_func()
    
    print("\n🎉 Visual examples complete!")
    print("\n📖 UNDERSTANDING BEVEL VS CHAMFER:")
    print("• BEVEL: Creates rounded, curved edges")
    print("  - Smooth transition from face to face")
    print("  - Good for organic, soft appearances")
    print("  - Works on all primitives")
    print()
    print("• CHAMFER: Creates flat, angled cuts on edges")
    print("  - Sharp transition with flat surfaces")
    print("  - Good for mechanical, architectural looks")
    print("  - Works best on boxes and cylinders")
    print()
    print("• PRIORITY: If both are set, bevel takes priority")
    print("• COMBINATION: Use with boolean operations and smooth radius")

if __name__ == "__main__":
    run_visual_examples()
