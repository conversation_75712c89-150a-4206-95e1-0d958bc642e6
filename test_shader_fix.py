"""
Test script to verify the shader fix for SDF widgets.
Run this in Blender's Text Editor to test the corrected shader.
"""

import bpy

def test_shader_creation():
    """Test that the shader can be created without errors"""
    print("🧪 Testing Shader Creation")
    print("=" * 40)
    
    try:
        import gpu
        from gpu_extras.batch import batch_for_shader
        
        # Test the corrected shader name
        shader = gpu.shader.from_builtin('UNIFORM_COLOR')
        print("✅ UNIFORM_COLOR shader created successfully")
        
        # Test creating a simple batch
        verts = [(0, 0, 0), (1, 0, 0)]
        batch = batch_for_shader(shader, 'LINES', {"pos": verts})
        print("✅ Batch created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Shader test failed: {e}")
        return False

def test_widget_enable():
    """Test enabling widgets with the shader fix"""
    print("\n🔄 Testing Widget Enable with Shader Fix")
    print("=" * 40)
    
    try:
        # Make sure we have an SDF item
        scene = bpy.context.scene
        if not hasattr(scene, 'sdf_tree'):
            print("❌ No SDF tree")
            return False
        
        tree = scene.sdf_tree
        if not tree.items:
            # Create a test item
            bpy.ops.sdf.tree_add_sphere()
            print("✅ Created test sphere")
        
        # Select the first item
        tree.active_index = 0
        print(f"✅ Selected item: {tree.items[0].name}")
        
        # Try to enable widgets
        bpy.ops.sdf.toggle_simple_widgets()
        print("✅ Widget toggle executed")
        
        # Check if widgets are enabled
        from .sdf_simple_widgets import _widget_state
        if _widget_state['enabled']:
            print("✅ Widgets are enabled")
            
            # Wait a moment and check for errors
            import time
            time.sleep(0.1)
            
            print("✅ No immediate errors after enabling")
            return True
        else:
            print("❌ Widgets not enabled")
            return False
            
    except Exception as e:
        print(f"❌ Widget enable test failed: {e}")
        return False

def check_console_for_errors():
    """Check if the shader error is gone"""
    print("\n📋 Checking for Shader Errors")
    print("=" * 40)
    
    print("Look at the console output above...")
    print("✅ If you see this message, the shader error should be fixed!")
    print("❌ If you still see '3D_UNIFORM_COLOR' errors, the fix didn't work")
    
    return True

def create_simple_test_scene():
    """Create a simple scene to test widgets"""
    print("\n🎬 Creating Simple Test Scene")
    print("=" * 40)
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ No SDF tree")
        return False
    
    tree = scene.sdf_tree
    
    # Clear and create test items
    tree.items.clear()
    tree.active_index = 0
    
    # Add a sphere at origin
    bpy.ops.sdf.tree_add_sphere()
    sphere = tree.items[0]
    sphere.name = "Widget Test Sphere"
    sphere.location = (0.0, 0.0, 0.0)
    sphere.radius = 1.0
    
    # Select it
    tree.active_index = 0
    
    print(f"✅ Created test scene with sphere at {tuple(sphere.location)}")
    return True

def run_shader_fix_test():
    """Run all shader fix tests"""
    print("🚀 Starting Shader Fix Tests")
    print("=" * 50)
    
    tests = [
        ("Shader Creation", test_shader_creation),
        ("Create Test Scene", create_simple_test_scene),
        ("Widget Enable", test_widget_enable),
        ("Check Console", check_console_for_errors),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} passed")
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} error: {e}")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 Shader fix tests completed!")
        print("\n📋 What to check now:")
        print("1. Look at the console - no more '3D_UNIFORM_COLOR' errors")
        print("2. Look at the 3D viewport - colored axes should appear")
        print("3. Try selecting different SDF items")
        print("4. Look for red/green/blue lines at item positions")
        print("5. The widget drawing should work without errors")
    else:
        print("\n⚠️  Some tests failed - check output above")
    
    return passed == total

if __name__ == "__main__":
    run_shader_fix_test()
