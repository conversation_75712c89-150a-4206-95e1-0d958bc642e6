"""
Safe debug script for SDF widgets that won't freeze <PERSON>len<PERSON>.
Run this in Blender's Text Editor to safely diagnose widget issues.
"""

import bpy

def safe_debug_widgets():
    """Safely debug widget system without GPU calls"""
    print("🔍 Safe Widget Debug")
    print("=" * 40)
    
    # Check SDF tree
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ No SDF tree found")
        return False
    
    tree = scene.sdf_tree
    print(f"✅ SDF tree: {len(tree.items)} items, active: {tree.active_index}")
    
    # Check active item
    if tree.items and 0 <= tree.active_index < len(tree.items):
        item = tree.items[tree.active_index]
        print(f"✅ Active item: {item.name} ({item.item_type})")
        print(f"   Local: {tuple(item.location)}")
        print(f"   World: {item.get_world_location(tree)}")
    else:
        print("❌ No valid active item")
        return False
    
    # Check widget state (safely)
    try:
        from .sdf_simple_widgets import _widget_state
        print(f"✅ Widget state: enabled={_widget_state['enabled']}")
        print(f"   Handler: {_widget_state['draw_handler'] is not None}")
    except Exception as e:
        print(f"❌ Widget state error: {e}")
        return False
    
    # Check viewport context
    if bpy.context.area and bpy.context.area.type == 'VIEW_3D':
        print("✅ In 3D viewport")
    else:
        print("⚠️  Not in 3D viewport")
    
    print("\n📋 Widget Status Summary:")
    print(f"• SDF Tree: {'✅' if hasattr(scene, 'sdf_tree') else '❌'}")
    print(f"• Active Item: {'✅' if tree.items and 0 <= tree.active_index < len(tree.items) else '❌'}")
    print(f"• Widget Enabled: {'✅' if _widget_state['enabled'] else '❌'}")
    print(f"• Draw Handler: {'✅' if _widget_state['draw_handler'] else '❌'}")
    print(f"• 3D Viewport: {'✅' if bpy.context.area and bpy.context.area.type == 'VIEW_3D' else '❌'}")
    
    return True

def create_test_scene():
    """Create a simple test scene for widgets"""
    print("\n🎬 Creating Test Scene")
    print("=" * 40)
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ No SDF tree - addon not properly loaded")
        return False
    
    tree = scene.sdf_tree
    
    # Clear and create test items
    tree.items.clear()
    tree.active_index = 0
    
    # Add a sphere
    bpy.ops.sdf.tree_add_sphere()
    sphere = tree.items[0]
    sphere.name = "Test Sphere"
    sphere.location = (0.0, 0.0, 0.0)
    sphere.radius = 1.0
    
    # Add a box
    bpy.ops.sdf.tree_add_box()
    box = tree.items[1]
    box.name = "Test Box"
    box.location = (3.0, 0.0, 0.0)
    box.size = (1.0, 1.0, 1.0)
    
    # Select the sphere
    tree.active_index = 0
    
    print(f"✅ Created test scene with {len(tree.items)} items")
    print(f"   Active: {tree.items[tree.active_index].name}")
    
    return True

def test_widget_toggle():
    """Test widget enable/disable safely"""
    print("\n🔄 Testing Widget Toggle")
    print("=" * 40)
    
    try:
        # Get initial state
        from .sdf_simple_widgets import _widget_state
        initial_state = _widget_state['enabled']
        print(f"Initial state: {initial_state}")
        
        # Toggle widgets
        bpy.ops.sdf.toggle_simple_widgets()
        new_state = _widget_state['enabled']
        print(f"After toggle: {new_state}")
        
        if new_state != initial_state:
            print("✅ Toggle works correctly")
            
            # Force viewport redraw
            for area in bpy.context.screen.areas:
                if area.type == 'VIEW_3D':
                    area.tag_redraw()
            print("✅ Viewport redraw triggered")
            
            return True
        else:
            print("❌ Toggle didn't change state")
            return False
            
    except Exception as e:
        print(f"❌ Toggle error: {e}")
        return False

def check_draw_handler():
    """Check if draw handler is properly registered"""
    print("\n🎨 Checking Draw Handler")
    print("=" * 40)
    
    try:
        from .sdf_simple_widgets import _widget_state, viewport_draw_handler
        
        if _widget_state['enabled']:
            print("✅ Widgets are enabled")
            
            if _widget_state['draw_handler']:
                print("✅ Draw handler is registered")
                print(f"   Handler ID: {_widget_state['draw_handler']}")
                
                # Check if handler function exists
                if callable(viewport_draw_handler):
                    print("✅ Draw handler function is callable")
                else:
                    print("❌ Draw handler function not callable")
                    return False
                
                return True
            else:
                print("❌ Draw handler not registered")
                return False
        else:
            print("⚠️  Widgets are disabled")
            return True  # This is OK
            
    except Exception as e:
        print(f"❌ Draw handler check error: {e}")
        return False

def run_safe_debug():
    """Run all safe debug tests"""
    print("🚀 Starting Safe Widget Debug")
    print("=" * 50)
    
    tests = [
        ("Create Test Scene", create_test_scene),
        ("Debug Widget State", safe_debug_widgets),
        ("Test Widget Toggle", test_widget_toggle),
        ("Check Draw Handler", check_draw_handler),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} passed")
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} error: {e}")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All safe debug tests passed!")
        print("\n📋 If widgets still don't appear:")
        print("1. Make sure you're in a 3D viewport")
        print("2. Try selecting different SDF items")
        print("3. Check if widgets are behind other objects")
        print("4. Try zooming out to see if widgets are far away")
        print("5. Look for thin colored lines (red/green/blue)")
    else:
        print("\n⚠️  Some tests failed - check output above")
    
    return passed == total

if __name__ == "__main__":
    run_safe_debug()
