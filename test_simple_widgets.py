"""
Test script for the simple SDF viewport widgets.
Run this in Blender's Text Editor to test the widget functionality.
"""

import bpy

def test_simple_widget_setup():
    """Test that simple widgets can be enabled"""
    print("🧪 Testing Simple Widget Setup...")
    
    # Check if the operator exists
    try:
        bpy.ops.sdf.toggle_simple_widgets()
        print("✅ Simple widgets operator found and executed")
        return True
    except Exception as e:
        print(f"❌ Simple widgets operator failed: {e}")
        return False

def test_interactive_move():
    """Test interactive move operators"""
    print("\n🧪 Testing Interactive Move Operators...")
    
    # Check if move operators exist
    operators_to_test = [
        ("sdf.move_item_interactive", "Interactive Move"),
    ]
    
    success = True
    for op_name, op_desc in operators_to_test:
        try:
            # Just check if the operator exists (don't actually run it)
            op = getattr(bpy.ops, op_name.split('.')[0])
            op_func = getattr(op, op_name.split('.')[1])
            print(f"✅ {op_desc} operator found")
        except Exception as e:
            print(f"❌ {op_desc} operator not found: {e}")
            success = False
    
    return success

def create_simple_widget_demo():
    """Create a demo scene for simple widgets"""
    print("\n🎬 Creating Simple Widget Demo...")
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ SDF tree not found")
        return False
    
    tree = scene.sdf_tree
    
    # Clear tree
    tree.items.clear()
    tree.active_index = 0
    
    # Create test objects
    print("Creating test objects...")
    
    # Main group
    bpy.ops.sdf.tree_add_group()
    main_group = tree.items[0]
    main_group.name = "Widget Demo Group"
    main_group.location = (0.0, 0.0, 0.0)
    
    # Add sphere child
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='SPHERE')
    sphere = tree.items[1]
    sphere.name = "Demo Sphere"
    sphere.location = (1.5, 0.0, 0.0)
    sphere.radius = 0.8
    
    # Add box child
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='BOX')
    box = tree.items[2]
    box.name = "Demo Box"
    box.location = (-1.5, 0.0, 0.0)
    box.size = (0.6, 0.6, 0.6)
    box.boolean_mode = 'UNION'
    
    # Add root cylinder
    bpy.ops.sdf.tree_add_cylinder()
    cylinder = tree.items[3]
    cylinder.name = "Demo Cylinder"
    cylinder.location = (0.0, 2.5, 0.0)
    cylinder.radius = 0.4
    cylinder.height = 1.2
    cylinder.boolean_mode = 'SUBTRACT'
    
    print("✅ Demo scene created!")
    print("\nDemo objects:")
    for i, item in enumerate(tree.items):
        indent = "  " * item.indent_level
        parent_info = f"(parent: {item.parent_index})" if item.parent_index >= 0 else "(root)"
        print(f"{i}: {indent}{item.name} {parent_info}")
        print(f"    {indent}Location: {tuple(item.location)}")
        if item.item_type != 'GROUP':
            world_loc = item.get_world_location(tree)
            print(f"    {indent}World: {world_loc}")
    
    # Select the main group
    tree.active_index = 0
    
    return True

def test_widget_drawing():
    """Test that widgets can be drawn"""
    print("\n🎨 Testing Widget Drawing...")
    
    # Import the drawing function
    try:
        from .sdf_simple_widgets import get_active_sdf_item, draw_simple_widget
        
        # Test getting active item
        item, tree = get_active_sdf_item()
        if item:
            print(f"✅ Active item found: {item.name}")
            print(f"   Location: {tuple(item.location)}")
            print(f"   World Location: {item.get_world_location(tree)}")
            
            # Test drawing (this won't actually draw since we're not in viewport context)
            try:
                # draw_simple_widget()  # Don't actually call this outside viewport
                print("✅ Widget drawing function accessible")
                return True
            except Exception as e:
                print(f"⚠️  Widget drawing test skipped (normal outside viewport): {e}")
                return True
        else:
            print("❌ No active item found for widget drawing")
            return False
            
    except Exception as e:
        print(f"❌ Widget drawing test failed: {e}")
        return False

def demo_simple_widgets():
    """Complete demo of simple widgets"""
    print("🎯 Simple Widget Demo")
    print("=" * 50)
    
    # Create demo scene
    success1 = create_simple_widget_demo()
    if not success1:
        return False
    
    # Test widget setup
    success2 = test_simple_widget_setup()
    if not success2:
        return False
    
    # Test interactive operators
    success3 = test_interactive_move()
    if not success3:
        return False
    
    # Test drawing system
    success4 = test_widget_drawing()
    if not success4:
        return False
    
    print("\n🎉 Simple widget demo complete!")
    print("\n📋 How to use the simple widgets:")
    print("1. ✅ Demo scene created with test objects")
    print("2. ✅ Simple widgets should be enabled")
    print("3. 🎯 Select different items in the SDF tree")
    print("4. 👀 Look for colored axes in the 3D viewport")
    print("5. 🖱️  Use the X/Y/Z buttons for interactive movement")
    print("6. 🎮 Click and drag to move objects along specific axes")
    
    print(f"\n🔧 Manual testing steps:")
    print(f"• Look at the 3D viewport - you should see colored axes")
    print(f"• Select different items in the SDF tree")
    print(f"• Notice how the widget moves to each item's world position")
    print(f"• Click the X/Y/Z buttons in the viewport panel")
    print(f"• Drag the mouse to move objects interactively")
    print(f"• Watch how children move with their parents!")
    
    return True

def run_simple_widget_tests():
    """Run all simple widget tests"""
    print("🚀 Starting Simple Widget Tests")
    print("=" * 60)
    
    tests = [
        demo_simple_widgets,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ Test passed\n")
            else:
                print("❌ Test failed\n")
        except Exception as e:
            print(f"❌ Test error: {e}\n")
            import traceback
            traceback.print_exc()
    
    print("=" * 60)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All simple widget tests passed!")
        print("\n🎮 Simple interactive widgets are ready!")
        print("• Toggle widgets with the button in the SDF panel")
        print("• Use X/Y/Z buttons for interactive movement")
        print("• Visual axes show in the viewport")
        return True
    else:
        print("⚠️  Some tests failed. Check the implementation.")
        return False

if __name__ == "__main__":
    run_simple_widget_tests()
