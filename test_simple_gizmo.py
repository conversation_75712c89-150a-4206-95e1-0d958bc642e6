"""
Test script for the simplified gizmo approach.
Run this in Blender's Text Editor to test the basic sync.
"""

import bpy
from mathutils import Vector

def create_simple_test():
    """Create a simple test scene"""
    print("🎯 Creating Simple Gizmo Test")
    print("=" * 30)
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ No SDF tree")
        return False
    
    tree = scene.sdf_tree
    
    # Clear and create one item
    tree.items.clear()
    tree.active_index = 0
    
    # Add a sphere at origin
    bpy.ops.sdf.tree_add_sphere()
    sphere = tree.items[0]
    sphere.name = "Simple Test"
    sphere.location = (0.0, 0.0, 0.0)
    sphere.radius = 1.0
    
    # Select it
    tree.active_index = 0
    
    print(f"✅ Created sphere at: {tuple(sphere.location)}")
    
    # Enable gizmos
    try:
        from .sdf_simple_widgets import _widget_state
        if not _widget_state['enabled']:
            bpy.ops.sdf.toggle_simple_widgets()
        
        print(f"✅ Gizmos enabled: {_widget_state['enabled']}")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_basic_movement():
    """Test basic movement"""
    print("\n🖱️  Testing Basic Movement")
    print("=" * 30)
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    if not tree.items:
        print("❌ No items")
        return False
    
    item = tree.items[0]
    
    print("Manual movement test:")
    print(f"Initial: {tuple(item.location)}")
    
    # Move in X
    item.location[0] = 1.0
    print(f"After X move: {tuple(item.location)}")
    print(f"World location: {item.get_world_location(tree)}")
    
    # Force redraw
    for area in bpy.context.screen.areas:
        if area.type == 'VIEW_3D':
            area.tag_redraw()
    
    # Move in Y
    item.location[1] = 1.0
    print(f"After Y move: {tuple(item.location)}")
    print(f"World location: {item.get_world_location(tree)}")
    
    # Force redraw
    for area in bpy.context.screen.areas:
        if area.type == 'VIEW_3D':
            area.tag_redraw()
    
    # Reset
    item.location = (0.0, 0.0, 0.0)
    for area in bpy.context.screen.areas:
        if area.type == 'VIEW_3D':
            area.tag_redraw()
    
    print(f"Reset to: {tuple(item.location)}")
    print("✅ Basic movement test complete")
    print("👀 Gizmo should have followed all movements")
    
    return True

def test_drag_sensitivity():
    """Test drag sensitivity"""
    print("\n🎚️  Testing Drag Sensitivity")
    print("=" * 30)
    
    try:
        from .sdf_simple_widgets import _widget_state
        
        # Check current sensitivity value
        print("Current sensitivity: 0.002 (hardcoded)")
        print("Mouse delta of 100px should move object by:")
        print(f"  X/Y: {100 * 0.002} = 0.2 units")
        print(f"  Z: {100 * 0.002} = 0.2 units")
        
        print("\n💡 This should feel reasonable for dragging")
        print("   If too fast: reduce sensitivity")
        print("   If too slow: increase sensitivity")
        
        return True
        
    except Exception as e:
        print(f"❌ Sensitivity test failed: {e}")
        return False

def provide_simple_test_instructions():
    """Provide simple testing instructions"""
    print("\n📋 Simple Test Instructions")
    print("=" * 30)
    
    print("1. 👀 Look at the 3D viewport")
    print("2. 🎯 You should see colored axes at (0,0,0)")
    print("3. 🖱️  Try dragging each axis:")
    print("   • Red line (X-axis) - drag left/right")
    print("   • Green line (Y-axis) - drag left/right")
    print("   • Blue line (Z-axis) - drag up/down")
    print("4. 👁️  Watch for:")
    print("   • Gizmo should move with the sphere")
    print("   • No offset should develop")
    print("   • Movement should be smooth")
    
    print(f"\n🔧 If there are still issues:")
    print(f"• Check console for error messages")
    print(f"• Try smaller mouse movements")
    print(f"• Make sure you're clicking on the colored lines")
    print(f"• Verify gizmos are enabled")
    
    print(f"\n✅ Expected behavior:")
    print(f"• Gizmo always at sphere center")
    print(f"• Smooth dragging along axes")
    print(f"• No speed mismatch")
    print(f"• Immediate response to movement")

def check_widget_state():
    """Check current widget state"""
    print("\n🔍 Checking Widget State")
    print("=" * 30)
    
    try:
        from .sdf_simple_widgets import _widget_state, get_active_sdf_item
        
        print(f"Enabled: {_widget_state['enabled']}")
        print(f"Dragging: {_widget_state['dragging']}")
        print(f"Drag axis: {_widget_state['drag_axis']}")
        print(f"Draw handler: {_widget_state['draw_handler'] is not None}")
        
        item, tree = get_active_sdf_item()
        if item:
            print(f"Active item: {item.name}")
            print(f"Location: {tuple(item.location)}")
            print(f"World: {item.get_world_location(tree)}")
        else:
            print("❌ No active item")
        
        return True
        
    except Exception as e:
        print(f"❌ State check failed: {e}")
        return False

def run_simple_tests():
    """Run all simple tests"""
    print("🚀 Starting Simple Gizmo Tests")
    print("=" * 40)
    
    tests = [
        ("Create Simple Test", create_simple_test),
        ("Check Widget State", check_widget_state),
        ("Test Basic Movement", test_basic_movement),
        ("Test Drag Sensitivity", test_drag_sensitivity),
        ("Test Instructions", provide_simple_test_instructions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} passed")
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} error: {e}")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed >= 4:
        print("\n🎉 Simple gizmo tests completed!")
        print("\n🎮 Try dragging the gizmo now!")
        print("The simplified approach should work better.")
    else:
        print("\n⚠️  Some tests failed - check output above")
    
    return passed >= 4

if __name__ == "__main__":
    run_simple_tests()
