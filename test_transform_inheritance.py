"""
Test script to verify that parent transforms affect child items.
Run this in <PERSON><PERSON>der's Text Editor to test transform inheritance.
"""

import bpy

def test_parent_child_transforms():
    """Test that moving a parent moves its children"""
    print("🧪 Testing Parent-Child Transform Inheritance...")
    
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("❌ SDF tree not found")
        return False
    
    tree = scene.sdf_tree
    
    # Clear tree
    tree.items.clear()
    tree.active_index = 0
    
    # Create a group (parent)
    bpy.ops.sdf.tree_add_group()
    parent_group = tree.items[0]
    parent_group.name = "Parent Group"
    parent_group.location = (0.0, 0.0, 0.0)  # Start at origin
    
    # Add a child sphere
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='SPHERE')
    child_sphere = tree.items[1]
    child_sphere.name = "Child Sphere"
    child_sphere.location = (1.0, 0.0, 0.0)  # Local offset from parent
    child_sphere.radius = 0.5
    
    print(f"✅ Created parent group with child sphere")
    print(f"   Parent location: {tuple(parent_group.location)}")
    print(f"   Child local location: {tuple(child_sphere.location)}")
    
    # Test initial world location
    child_world_location = child_sphere.get_world_location(tree)
    expected_world_location = (1.0, 0.0, 0.0)  # Parent (0,0,0) + Child (1,0,0)
    
    print(f"   Child world location: {child_world_location}")
    print(f"   Expected world location: {expected_world_location}")
    
    if abs(child_world_location[0] - expected_world_location[0]) < 0.001:
        print("✅ Initial world location calculation correct")
    else:
        print("❌ Initial world location calculation incorrect")
        return False
    
    # Now move the parent and test again
    print("\nMoving parent group to (2, 1, 0)...")
    parent_group.location = (2.0, 1.0, 0.0)
    
    # Test new world location
    new_child_world_location = child_sphere.get_world_location(tree)
    new_expected_world_location = (3.0, 1.0, 0.0)  # Parent (2,1,0) + Child (1,0,0)
    
    print(f"   New child world location: {new_child_world_location}")
    print(f"   New expected world location: {new_expected_world_location}")
    
    if (abs(new_child_world_location[0] - new_expected_world_location[0]) < 0.001 and
        abs(new_child_world_location[1] - new_expected_world_location[1]) < 0.001):
        print("✅ Parent transform inheritance working!")
    else:
        print("❌ Parent transform inheritance not working")
        return False
    
    # Test GLSL generation with transforms
    print("\nTesting GLSL generation with transforms...")
    try:
        glsl = tree.generate_glsl()
        print(f"Generated GLSL:\n{glsl}")
        
        # Check if the world location is used in GLSL
        if "vec3(3.0, 1.0, 0.0)" in glsl or "vec3(3, 1, 0)" in glsl:
            print("✅ GLSL uses world transform")
        else:
            print("⚠️  GLSL might not be using world transform")
            print("   (This might be OK if the child is processed differently)")
        
        return True
        
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False

def test_nested_groups():
    """Test transforms with nested groups (grandparent, parent, child)"""
    print("\n🧪 Testing Nested Group Transforms...")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Clear tree
    tree.items.clear()
    tree.active_index = 0
    
    # Create grandparent group
    bpy.ops.sdf.tree_add_group()
    grandparent = tree.items[0]
    grandparent.name = "Grandparent"
    grandparent.location = (1.0, 0.0, 0.0)
    
    # Create parent group as child of grandparent
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='GROUP')
    parent = tree.items[1]
    parent.name = "Parent"
    parent.location = (0.0, 1.0, 0.0)
    
    # Create child sphere as child of parent
    tree.active_index = 1
    bpy.ops.sdf.tree_add_as_child(item_type='SPHERE')
    child = tree.items[2]
    child.name = "Child"
    child.location = (0.0, 0.0, 1.0)
    child.radius = 0.3
    
    print(f"✅ Created 3-level hierarchy")
    print(f"   Grandparent: {tuple(grandparent.location)}")
    print(f"   Parent: {tuple(parent.location)}")
    print(f"   Child: {tuple(child.location)}")
    
    # Test world location calculation
    child_world_location = child.get_world_location(tree)
    expected_world_location = (1.0, 1.0, 1.0)  # Sum of all parent locations
    
    print(f"   Child world location: {child_world_location}")
    print(f"   Expected: {expected_world_location}")
    
    if (abs(child_world_location[0] - expected_world_location[0]) < 0.001 and
        abs(child_world_location[1] - expected_world_location[1]) < 0.001 and
        abs(child_world_location[2] - expected_world_location[2]) < 0.001):
        print("✅ Nested group transforms working!")
        return True
    else:
        print("❌ Nested group transforms not working")
        return False

def test_multiple_children():
    """Test that multiple children all inherit parent transforms"""
    print("\n🧪 Testing Multiple Children Transform Inheritance...")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Clear tree
    tree.items.clear()
    tree.active_index = 0
    
    # Create parent group
    bpy.ops.sdf.tree_add_group()
    parent = tree.items[0]
    parent.name = "Parent"
    parent.location = (5.0, 0.0, 0.0)
    
    # Add multiple children
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='SPHERE')
    child1 = tree.items[1]
    child1.name = "Child 1"
    child1.location = (1.0, 0.0, 0.0)
    
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='BOX')
    child2 = tree.items[2]
    child2.name = "Child 2"
    child2.location = (0.0, 1.0, 0.0)
    
    tree.active_index = 0
    bpy.ops.sdf.tree_add_as_child(item_type='CYLINDER')
    child3 = tree.items[3]
    child3.name = "Child 3"
    child3.location = (-1.0, 0.0, 0.0)
    
    print(f"✅ Created parent with 3 children")
    
    # Test all children's world locations
    child1_world = child1.get_world_location(tree)
    child2_world = child2.get_world_location(tree)
    child3_world = child3.get_world_location(tree)
    
    expected1 = (6.0, 0.0, 0.0)  # Parent (5,0,0) + Child1 (1,0,0)
    expected2 = (5.0, 1.0, 0.0)  # Parent (5,0,0) + Child2 (0,1,0)
    expected3 = (4.0, 0.0, 0.0)  # Parent (5,0,0) + Child3 (-1,0,0)
    
    print(f"   Child 1 world: {child1_world}, expected: {expected1}")
    print(f"   Child 2 world: {child2_world}, expected: {expected2}")
    print(f"   Child 3 world: {child3_world}, expected: {expected3}")
    
    success = True
    for actual, expected in [(child1_world, expected1), (child2_world, expected2), (child3_world, expected3)]:
        if not all(abs(a - e) < 0.001 for a, e in zip(actual, expected)):
            success = False
            break
    
    if success:
        print("✅ All children inherit parent transforms correctly!")
        return True
    else:
        print("❌ Some children don't inherit parent transforms correctly")
        return False

def run_transform_tests():
    """Run all transform inheritance tests"""
    print("🚀 Starting Transform Inheritance Tests")
    print("=" * 60)
    
    tests = [
        test_parent_child_transforms,
        test_nested_groups,
        test_multiple_children,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ Test passed\n")
            else:
                print("❌ Test failed\n")
        except Exception as e:
            print(f"❌ Test error: {e}\n")
            import traceback
            traceback.print_exc()
    
    print("=" * 60)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All transform inheritance tests passed!")
        print("Parent transforms should now affect child positions!")
        return True
    else:
        print("⚠️  Some tests failed. Check the implementation.")
        return False

if __name__ == "__main__":
    run_transform_tests()
