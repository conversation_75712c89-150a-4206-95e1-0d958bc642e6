# Gizmo Synchronization Fix

## Problem Description

The gizmos were moving faster than the primitives when dragging primitives in the viewport. This happened because:

1. **Gizmo moves primitive**: When dragging a gizmo, the `modal` method updates the primitive's location
2. **Only shader refreshes**: The code only called `SDFRenderer.refresh_shader()` to update the viewport
3. **Gizmo position not updated**: The gizmo's visual position was never refreshed to match the new primitive location
4. **Accumulating delta**: The gizmo continued to accumulate mouse deltas from its original position while the primitive moved away

## Root Cause

In the gizmo modal methods (`SDF_GT_TranslateGizmo.modal`, `SDF_GT_RotateGizmo.modal`, `SDF_GT_ScaleGizmo.modal`), after updating the primitive location, the code only refreshed the shader but **did not refresh the gizmo positions**.

## Solution

Added a call to `self._refresh_gizmo_positions(context)` after updating the primitive location in all gizmo modal methods. This ensures that:

1. **Primitive location is updated** (existing behavior)
2. **Shader is refreshed** (existing behavior) 
3. **Gizmo positions are refreshed** (NEW - the fix!)

## Code Changes

### Before (in `sdf_gizmos.py`):
```python
def modal(self, context, event, tweak):
    if event.type == 'MOUSEMOVE':
        # ... update primitive location ...
        
        # Update viewport
        self._update_viewport()  # Only refreshes shader
        
    return {'RUNNING_MODAL'}
```

### After (in `sdf_gizmos.py`):
```python
def modal(self, context, event, tweak):
    if event.type == 'MOUSEMOVE':
        # ... update primitive location ...
        
        # Update viewport
        self._update_viewport()  # Refreshes shader
        
        # Refresh gizmo positions to stay synchronized with primitive
        self._refresh_gizmo_positions(context)  # NEW - the fix!
        
    return {'RUNNING_MODAL'}
```

### New Method Added:
```python
def _refresh_gizmo_positions(self, context):
    """Refresh the gizmo group positions to stay synchronized with primitive"""
    try:
        # Find the gizmo group that contains this gizmo
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                for space in area.spaces:
                    if space.type == 'VIEW_3D':
                        for gizmo_group in space.gizmos:
                            if hasattr(gizmo_group, 'refresh'):
                                gizmo_group.refresh(context)
                                break
    except:
        pass
```

## Files Modified

- `sdf_gizmos.py`: Added `_refresh_gizmo_positions()` method to all three gizmo classes and called it in their modal methods
- `sdf_simple_widgets.py`: Added gizmo refresh calls to the simple widgets system and interactive move operators
- `tree_system.py`: Added gizmo refresh to the property update callback so UI changes also refresh gizmos

## How It Works

1. **User drags gizmo**: Mouse movement triggers the gizmo's `modal` method
2. **Primitive moves**: The primitive's location is updated based on the mouse delta
3. **Shader refreshes**: The SDF shader is updated to show the new primitive position
4. **Gizmo refreshes**: The gizmo group's `refresh` method is called, which:
   - Calculates the primitive's new world location using `item.get_world_location(tree)`
   - Updates all gizmo positions to match: `gizmo.matrix_basis = Matrix.Translation(location)`
5. **Gizmo stays synchronized**: The gizmo visually moves with the primitive

## Testing

To test the fix:

1. **Enable gizmos**: Use the SDF Simple Widgets toggle
2. **Create a primitive**: Add a sphere or box to the SDF tree
3. **Drag the gizmo**: Use the X/Y/Z axis handles to move the primitive
4. **Verify synchronization**: The gizmo should stay exactly at the primitive's location during dragging

## Benefits

- **Eliminates "fast gizmo" effect**: Gizmos now move at the same speed as primitives
- **Maintains visual feedback**: Users can see exactly where they're moving the primitive
- **Works with nested primitives**: Correctly handles world vs local coordinates
- **Consistent behavior**: All transform gizmos (translate, rotate, scale) now behave consistently
- **Comprehensive coverage**: Gizmos stay synchronized whether moved via:
  - Direct gizmo dragging
  - Simple widgets system
  - Interactive X/Y/Z buttons
  - Direct property editing in the UI panels

## Alternative Approaches Considered

1. **Set gizmo location directly**: Instead of refreshing the entire gizmo group, we could set `self.matrix_basis` directly. However, this wouldn't handle the coordinate transformations properly.

2. **Use Blender's built-in gizmos**: We could switch to Blender's native transform gizmos, but this would require significant refactoring and might not integrate well with the SDF system.

3. **Disable gizmo updates during drag**: We could prevent gizmo position updates during dragging, but this would make the visual feedback confusing.

The chosen solution is the most robust because it leverages the existing `refresh` method that already handles all the coordinate calculations correctly.
