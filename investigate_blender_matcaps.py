"""
<PERSON><PERSON><PERSON> to investigate how to access <PERSON><PERSON>der's built-in matcaps.
Run this in <PERSON>lender to explore the matcap system.
"""

import bpy
import os


def investigate_viewport_shading():
    """Investigate viewport shading properties"""
    print("\n=== Investigating Viewport Shading ===")
    
    try:
        # Get the current 3D viewport
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                space = area.spaces[0]
                shading = space.shading
                
                print(f"Shading type: {shading.type}")
                print(f"Color type: {shading.color_type}")
                print(f"Light: {shading.light}")
                print(f"Studio light: {shading.studio_light}")
                print(f"Use world space lighting: {shading.use_world_space_lighting}")
                
                # Check if there are matcap-related properties
                print("\nAll shading properties:")
                for attr in dir(shading):
                    if not attr.startswith('_'):
                        try:
                            value = getattr(shading, attr)
                            if not callable(value):
                                print(f"  {attr}: {value}")
                        except:
                            print(f"  {attr}: <error accessing>")
                
                break
        
        return True
        
    except Exception as e:
        print(f"Error investigating viewport shading: {e}")
        import traceback
        traceback.print_exc()
        return False


def investigate_studio_lights():
    """Investigate available studio lights"""
    print("\n=== Investigating Studio Lights ===")
    
    try:
        # Check if there's a way to list studio lights
        print("Checking bpy.utils for studio light functions...")
        
        if hasattr(bpy.utils, 'previews'):
            print("bpy.utils.previews found")
            
        # Check context for studio light info
        if hasattr(bpy.context, 'preferences'):
            prefs = bpy.context.preferences
            print(f"Preferences found: {type(prefs)}")
            
            if hasattr(prefs, 'studio_lights'):
                print("Studio lights in preferences found!")
                studio_lights = prefs.studio_lights
                print(f"Studio lights type: {type(studio_lights)}")
                
                # Try to list studio lights
                for attr in dir(studio_lights):
                    if not attr.startswith('_'):
                        try:
                            value = getattr(studio_lights, attr)
                            print(f"  {attr}: {value}")
                        except:
                            print(f"  {attr}: <error accessing>")
        
        # Check if we can access studio light files directly
        print("\nChecking for studio light files...")
        
        # Blender typically stores studio lights in the installation directory
        blender_path = bpy.utils.resource_path('LOCAL')
        print(f"Blender local path: {blender_path}")
        
        if blender_path:
            studio_light_paths = [
                os.path.join(blender_path, 'datafiles', 'studiolights'),
                os.path.join(blender_path, 'datafiles', 'studiolights', 'matcap'),
                os.path.join(blender_path, 'datafiles', 'studiolights', 'world'),
            ]
            
            for path in studio_light_paths:
                if os.path.exists(path):
                    print(f"Found studio light directory: {path}")
                    files = os.listdir(path)
                    print(f"  Files: {files[:10]}...")  # Show first 10 files
                else:
                    print(f"Studio light path not found: {path}")
        
        return True
        
    except Exception as e:
        print(f"Error investigating studio lights: {e}")
        import traceback
        traceback.print_exc()
        return False


def investigate_matcap_access():
    """Try different ways to access matcaps"""
    print("\n=== Investigating Matcap Access ===")
    
    try:
        # Method 1: Check if matcaps are loaded as images
        print("Method 1: Checking bpy.data.images for matcaps...")
        for image in bpy.data.images:
            if 'matcap' in image.name.lower() or 'studio' in image.name.lower():
                print(f"  Found potential matcap image: {image.name}")
        
        # Method 2: Check current viewport matcap
        print("\nMethod 2: Checking current viewport matcap...")
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                space = area.spaces[0]
                shading = space.shading
                
                if shading.type == 'SOLID' and shading.light == 'MATCAP':
                    print(f"Current matcap: {shading.studio_light}")
                    
                    # Try to change matcap and see what's available
                    print("Trying to access studio light enum items...")
                    
                    # This might give us the available options
                    try:
                        # Get the RNA property info
                        rna_prop = shading.bl_rna.properties.get('studio_light')
                        if rna_prop and hasattr(rna_prop, 'enum_items'):
                            print("Available studio lights:")
                            for item in rna_prop.enum_items:
                                print(f"  {item.identifier}: {item.name}")
                    except Exception as enum_error:
                        print(f"Error accessing enum items: {enum_error}")
                
                break
        
        # Method 3: Try to access through preferences
        print("\nMethod 3: Checking preferences for studio lights...")
        try:
            prefs = bpy.context.preferences
            if hasattr(prefs, 'studio_lights'):
                studio_lights = prefs.studio_lights
                print(f"Studio lights collection: {studio_lights}")
                
                # Try to iterate through studio lights
                try:
                    for i, light in enumerate(studio_lights):
                        print(f"  Studio light {i}: {light}")
                        if hasattr(light, 'name'):
                            print(f"    Name: {light.name}")
                        if hasattr(light, 'path'):
                            print(f"    Path: {light.path}")
                        if i >= 5:  # Limit output
                            print("    ... (more)")
                            break
                except Exception as iter_error:
                    print(f"Error iterating studio lights: {iter_error}")
        except Exception as prefs_error:
            print(f"Error accessing preferences: {prefs_error}")
        
        return True
        
    except Exception as e:
        print(f"Error investigating matcap access: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_matcap_switching():
    """Test switching between different matcaps"""
    print("\n=== Testing Matcap Switching ===")
    
    try:
        # Find a 3D viewport and set it to matcap mode
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                space = area.spaces[0]
                shading = space.shading
                
                # Set to solid shading with matcap
                original_type = shading.type
                original_light = shading.light
                original_studio_light = shading.studio_light
                
                print(f"Original settings: type={original_type}, light={original_light}, studio_light={original_studio_light}")
                
                # Switch to matcap mode
                shading.type = 'SOLID'
                shading.light = 'MATCAP'
                
                print(f"Switched to matcap mode")
                print(f"Current studio light: {shading.studio_light}")
                
                # Try some common matcap names
                test_matcaps = [
                    'basic_1.exr',
                    'check_normal+y.exr', 
                    'clay_brown.exr',
                    'metal_carpaint.exr',
                    'toon.exr',
                ]
                
                for matcap_name in test_matcaps:
                    try:
                        shading.studio_light = matcap_name
                        print(f"Successfully set matcap to: {matcap_name}")
                        break
                    except Exception as set_error:
                        print(f"Failed to set matcap {matcap_name}: {set_error}")
                
                # Restore original settings
                shading.type = original_type
                shading.light = original_light
                shading.studio_light = original_studio_light
                
                break
        
        return True
        
    except Exception as e:
        print(f"Error testing matcap switching: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_investigations():
    """Run all matcap investigations"""
    print("=" * 60)
    print("INVESTIGATING BLENDER'S MATCAP SYSTEM")
    print("=" * 60)
    
    investigations = [
        ("Viewport Shading", investigate_viewport_shading),
        ("Studio Lights", investigate_studio_lights),
        ("Matcap Access", investigate_matcap_access),
        ("Matcap Switching", test_matcap_switching),
    ]
    
    for name, func in investigations:
        print(f"\n{'='*20} {name} {'='*20}")
        try:
            func()
        except Exception as e:
            print(f"Investigation '{name}' failed: {e}")
    
    print("\n" + "=" * 60)
    print("INVESTIGATION COMPLETE")
    print("=" * 60)


if __name__ == "__main__":
    run_all_investigations()
