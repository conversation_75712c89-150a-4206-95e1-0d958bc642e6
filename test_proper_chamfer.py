#!/usr/bin/env python3
"""
Test the proper chamfer implementation.
<PERSON><PERSON><PERSON> should cut the edges at 45-degree angles, not just make the cube smaller.
"""

import bpy

def clear_tree():
    """Clear the SDF tree"""
    scene = bpy.context.scene
    if hasattr(scene, 'sdf_tree'):
        tree = scene.sdf_tree
        tree.items.clear()
        tree.active_index = 0
        return True
    return False

def test_proper_chamfer():
    """Test that chamfer creates proper 45-degree cuts on edges"""
    print("🔧 TESTING PROPER CHAMFER IMPLEMENTATION")
    print("=" * 50)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Normal box for reference
    bpy.ops.sdf.tree_add_box()
    normal_box = tree.items[-1]
    normal_box.name = "Normal Box"
    normal_box.size = (1.0, 1.0, 1.0)
    normal_box.location = (-2.0, 0.0, 0.0)
    normal_box.boolean_mode = 'UNION'
    
    # Chamfered box - should have cut edges, not be smaller overall
    bpy.ops.sdf.tree_add_box()
    chamfered_box = tree.items[-1]
    chamfered_box.name = "Chamfered Box"
    chamfered_box.size = (1.0, 1.0, 1.0)
    chamfered_box.location = (0.0, 0.0, 0.0)
    chamfered_box.chamfer_size = 0.2
    chamfered_box.boolean_mode = 'UNION'
    
    # Beveled box for comparison
    bpy.ops.sdf.tree_add_box()
    beveled_box = tree.items[-1]
    beveled_box.name = "Beveled Box"
    beveled_box.size = (1.0, 1.0, 1.0)
    beveled_box.location = (2.0, 0.0, 0.0)
    beveled_box.bevel_radius = 0.2
    beveled_box.boolean_mode = 'UNION'
    
    print("✅ Created proper chamfer test:")
    print(f"  Left:   {normal_box.name} - Sharp edges (reference)")
    print(f"  Center: {chamfered_box.name} - Should have 45° cuts on edges")
    print(f"  Right:  {beveled_box.name} - Should have rounded edges")
    print()
    print("🎯 Expected result for chamfer:")
    print("  • Main faces should be the same size as normal box")
    print("  • Edges should be cut at 45-degree angles")
    print("  • Should create new flat surfaces where cuts are made")
    print("  • Should NOT just be a smaller version of the normal box")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        print("\n✅ GLSL generated successfully")
        
        # Check for expected functions
        if "sdBoxChamfered" in glsl_code:
            print("✅ Chamfered box function found in GLSL")
            print("\n📋 Generated GLSL:")
            print("---")
            print(glsl_code)
            print("---")
            return True
        else:
            print("❌ Chamfered box function NOT found in GLSL")
            return False
            
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False

def test_chamfer_progression():
    """Test chamfer with different sizes to see the progression"""
    print("\n🔧 TESTING CHAMFER PROGRESSION")
    print("=" * 50)
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    chamfer_sizes = [0.0, 0.1, 0.2, 0.3, 0.4]
    
    for i, chamfer_size in enumerate(chamfer_sizes):
        bpy.ops.sdf.tree_add_box()
        box = tree.items[-1]
        box.name = f"Chamfer {chamfer_size}"
        box.size = (0.8, 0.8, 0.8)
        box.location = (i * 1.8 - 3.6, 0.0, 0.0)
        box.chamfer_size = chamfer_size
        box.boolean_mode = 'UNION'
        
        if chamfer_size == 0.0:
            print(f"  Box {i+1}: No chamfer - Normal sharp edges")
        else:
            print(f"  Box {i+1}: Chamfer {chamfer_size} - Bigger cuts on edges")
    
    print("\n🎯 Expected progression:")
    print("  • Box 1: Normal sharp-edged cube")
    print("  • Box 2: Small 45° cuts on edges")
    print("  • Box 3: Medium 45° cuts on edges")
    print("  • Box 4: Large 45° cuts on edges")
    print("  • Box 5: Very large 45° cuts on edges")
    print("  • Main faces should stay roughly the same size")
    print("  • Only the edges should be progressively cut more")
    
    # Test GLSL generation
    try:
        glsl_code = tree.generate_glsl()
        
        # Count different box types
        normal_count = glsl_code.count("sdBox(") - glsl_code.count("sdBoxChamfered") - glsl_code.count("sdBoxBeveled")
        chamfer_count = glsl_code.count("sdBoxChamfered")
        
        print(f"\n✅ GLSL contains:")
        print(f"  • {normal_count} normal box (chamfer = 0.0)")
        print(f"  • {chamfer_count} chamfered boxes (chamfer > 0.0)")
        
        if normal_count == 1 and chamfer_count == 4:
            print("✅ Correct progression of chamfer sizes!")
            return True
        else:
            print("❌ Unexpected box counts in GLSL")
            return False
            
    except Exception as e:
        print(f"❌ GLSL generation failed: {e}")
        return False

def test_chamfer_explanation():
    """Explain what proper chamfer should look like"""
    print("\n📖 CHAMFER EXPLANATION")
    print("=" * 50)
    
    print("What CHAMFER should do:")
    print()
    print("BEFORE (Normal Box):")
    print("    ┌─────────┐")
    print("    │         │")
    print("    │         │  ← Sharp edges")
    print("    │         │")
    print("    └─────────┘")
    print()
    print("AFTER (Chamfered Box):")
    print("    ┌─┐─────┐─┐")
    print("    │ │     │ │")
    print("    │ │     │ │  ← 45° cuts on edges")
    print("    │ │     │ │")
    print("    └─┘─────┘─┘")
    print()
    print("Key points:")
    print("• Main faces stay the same size")
    print("• Edges are cut at 45-degree angles")
    print("• Creates new flat surfaces on the cuts")
    print("• Does NOT just make the whole box smaller")
    print("• Each edge gets its own flat cut")
    print()
    print("What chamfer should NOT do:")
    print("❌ Make the entire box smaller")
    print("❌ Create rounded edges (that's bevel)")
    print("❌ Split the box into pieces")
    print("❌ Make weird geometry")

def run_proper_chamfer_tests():
    """Run all proper chamfer tests"""
    print("🔧 TESTING PROPER CHAMFER IMPLEMENTATION")
    print("=" * 60)
    print("Chamfer should cut edges at 45°, not make the cube smaller!")
    
    # Show explanation first
    test_chamfer_explanation()
    
    tests = [
        ("Proper Chamfer", test_proper_chamfer),
        ("Chamfer Progression", test_chamfer_progression),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 PROPER CHAMFER TEST RESULTS:")
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Chamfer should now work properly!")
        print("\n✅ WHAT SHOULD WORK NOW:")
        print("• Chamfer cuts edges at 45-degree angles")
        print("• Main faces stay the same size")
        print("• Creates new flat surfaces on the cuts")
        print("• Larger chamfer = bigger cuts on edges")
        print("• Does NOT just make the cube smaller")
        print("\n💡 TIP: Look at the edges of the chamfered box")
        print("You should see flat cuts where the sharp edges used to be!")
    else:
        print(f"\n⚠️ {len(results) - passed} test(s) failed.")
        print("The chamfer implementation may still need work.")
        print("\n🔍 If chamfer is still not working correctly:")
        print("• Make sure the addon was reloaded")
        print("• Try medium values (0.1-0.3)")
        print("• Look specifically at the edges, not the overall size")
        print("• Compare with a normal box to see the difference")

if __name__ == "__main__":
    run_proper_chamfer_tests()
