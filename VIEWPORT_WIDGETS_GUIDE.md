# SDF Viewport Widgets Guide

## Overview

The Arcane SDF addon now includes **interactive viewport widgets** that allow you to manipulate SDF item transforms directly in the 3D viewport using the mouse. This provides an intuitive, visual way to position and orient your SDF primitives and groups.

## Features

### 🎯 **Transform Gizmos**
- **Translation Arrows**: Red (X), <PERSON> (Y), <PERSON> (Z) axes for moving objects
- **Visual Feedback**: Real-time updates as you drag
- **Hierarchical Support**: Moving parents moves all children
- **World Coordinates**: Widgets show at the item's world position

### 🖱️ **Mouse Interaction**
- **Click and Drag**: Grab widget handles to transform objects
- **Real-time Updates**: See changes immediately in the viewport
- **Precision Control**: Fine-grained control over positioning
- **Visual Indicators**: Clear axis colors and center point

## How to Use

### 1. Enable Viewport Widgets

**Method 1: UI Button**
1. Open the Arcane SDF panel in the 3D viewport sidebar
2. Look for the "Viewport" section
3. Click "Toggle Transform Widgets" button

**Method 2: Operator**
- Press `F3` and search for "Toggle Viewport Widgets"
- Or run `bpy.ops.sdf.toggle_viewport_widgets()` in console

### 2. Select an SDF Item

1. In the SDF Tree panel, click on any item to select it
2. The viewport widgets will appear at the item's world position
3. You'll see colored axes: Red (X), Green (Y), Blue (Z)

### 3. Transform Objects

**Translation:**
- **Red Arrow**: Drag to move along X-axis
- **Green Arrow**: Drag to move along Y-axis  
- **Blue Arrow**: Drag to move along Z-axis
- **White Center**: Shows the item's position

**Visual Feedback:**
- The SDF shape updates in real-time as you drag
- Children automatically move with their parents
- Transform values update in the properties panel

## Widget Behavior

### 🌳 **Hierarchical Transforms**

When you move a parent item using widgets:
```
Parent Group (moved with widget)
├── Child 1 (moves automatically)
├── Child 2 (moves automatically)
└── Sub Group (moves automatically)
    ├── Grandchild 1 (moves automatically)
    └── Grandchild 2 (moves automatically)
```

### 🎯 **Widget Positioning**

Widgets always appear at the **world position** of the selected item:
- **Root items**: Widget at item's location
- **Child items**: Widget at parent location + child local offset
- **Nested items**: Widget at combined transform of all parents

### 🔄 **Real-time Updates**

- **Viewport**: SDF shape updates immediately
- **Properties**: Location values update in real-time
- **Tree**: All dependent items update automatically
- **Shader**: GLSL regenerates with new positions

## Technical Details

### 🎨 **Rendering System**

The widgets use Blender's modern GPU drawing system:
- **GPU Shaders**: Hardware-accelerated rendering
- **Batch Drawing**: Efficient geometry rendering
- **Depth Testing**: Proper 3D occlusion
- **Alpha Blending**: Semi-transparent widgets

### 🖱️ **Interaction System**

**Mouse Handling:**
- **Modal Operator**: Captures mouse events during interaction
- **Hit Testing**: Determines which axis was clicked
- **Delta Calculation**: Converts mouse movement to world coordinates
- **Sensitivity Control**: Adjustable movement speed

**State Management:**
- **Active Tracking**: Knows which item is selected
- **Drag State**: Tracks ongoing interactions
- **Axis Locking**: Constrains movement to selected axis

### ⚡ **Performance**

**Optimizations:**
- **Conditional Drawing**: Only draws when SDF viewport is active
- **Efficient Updates**: Only regenerates GLSL when needed
- **Minimal Geometry**: Simple lines and points for widgets
- **State Caching**: Avoids redundant calculations

## Troubleshooting

### 🔧 **Common Issues**

**Widgets Not Appearing:**
1. Check that "Toggle Transform Widgets" is enabled
2. Ensure SDF viewport rendering is active
3. Verify an SDF item is selected in the tree
4. Check that the item has a valid location

**Widgets Not Responding:**
1. Make sure you're clicking directly on the colored axes
2. Check that the modal operator is running
3. Verify the selected item can be transformed
4. Try toggling widgets off and on again

**Performance Issues:**
1. Disable widgets when not needed
2. Reduce the number of SDF items if viewport is slow
3. Check for console errors that might indicate problems

### 🐛 **Debug Information**

**Console Messages:**
- Widget enable/disable status
- Draw handler registration
- Mouse interaction events
- Transform update confirmations

**Error Checking:**
- Invalid item selection
- Missing SDF tree
- GPU drawing failures
- Modal operator issues

## Advanced Usage

### 🎮 **Keyboard Shortcuts**

While widgets are active:
- **ESC**: Cancel current drag operation
- **Enter**: Confirm current drag operation
- **Tab**: Switch between transform modes (future feature)

### 🔧 **Customization**

**Widget Appearance:**
- Axis colors: Red (X), Green (Y), Blue (Z)
- Line width: 3 pixels for visibility
- Alpha blending: Semi-transparent for depth perception
- Center point: White dot for position reference

**Sensitivity Settings:**
- Default: 0.01 units per pixel
- Adjustable in code for different workflows
- Can be modified per-axis if needed

### 🚀 **Future Enhancements**

Planned improvements:
- **Rotation Widgets**: Circular gizmos for rotation
- **Scale Widgets**: Handles for scaling operations
- **Constraint Modes**: Lock to specific axes or planes
- **Snap Settings**: Grid snapping and increment modes
- **Multi-Selection**: Transform multiple items at once
- **Custom Sensitivity**: User-adjustable movement speed

## Integration with SDF System

### 🔗 **Transform Inheritance**

Widgets work seamlessly with the hierarchical transform system:
- Moving a parent updates all children automatically
- World coordinates are calculated correctly
- GLSL generation uses updated positions
- Viewport rendering reflects changes immediately

### 🎯 **Selection Sync**

Widgets stay synchronized with the SDF tree:
- Selecting different items moves the widget
- Widget position updates with item transforms
- Active item highlighting in tree and viewport
- Consistent behavior across UI elements

### 📊 **Property Integration**

Widget transforms update property panels:
- Location values change in real-time
- Undo/redo system captures widget operations
- Property changes also update widget position
- Bidirectional synchronization

## Best Practices

### 🎯 **Workflow Tips**

1. **Enable widgets when modeling**: Turn on for interactive work
2. **Disable when rendering**: Turn off for final output
3. **Use with hierarchy**: Take advantage of parent-child relationships
4. **Combine with properties**: Use both widgets and numeric input
5. **Test frequently**: Check results in viewport regularly

### ⚡ **Performance Tips**

1. **Toggle as needed**: Don't leave widgets on unnecessarily
2. **Simplify scenes**: Fewer items = better performance
3. **Use efficiently**: Make deliberate, planned movements
4. **Monitor console**: Watch for error messages

---

**Note**: Viewport widgets provide an intuitive way to manipulate SDF transforms, making the modeling process more interactive and visual. They complement the existing property panels and tree hierarchy system for a complete modeling experience.
