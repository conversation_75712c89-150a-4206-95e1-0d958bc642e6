"""
SDF Viewport Gizmos for interactive transform manipulation.
Provides 3D widgets in the viewport to manipulate SDF item transforms with the mouse.
"""

import bpy
from bpy.types import Gizmo, GizmoGroup
from mathutils import Vector, Matrix
import math

class SDF_GT_TranslateGizmo(Gizmo):
    """Translation gizmo for SDF items"""
    bl_idname = "SDF_GT_translate_gizmo"

    def setup(self):
        """Setup the gizmo"""
        # Use built-in arrow shape
        if hasattr(self, 'use_draw_modal'):
            self.use_draw_modal = True

        # Set up basic properties
        self.scale_basis = 1.0
        self.line_width = 3

    def draw(self, context):
        """Draw the gizmo"""
        # Use built-in drawing if available
        if hasattr(self, 'draw_preset'):
            self.draw_preset('PLAIN_AXES')
        else:
            # Fallback to simple drawing
            pass

    def draw_select(self, context, select_id):
        """Draw for selection"""
        if hasattr(self, 'draw_preset'):
            self.draw_preset('PLAIN_AXES', select_id=select_id)

    def invoke(self, context, event):
        """Start interaction"""
        return {'RUNNING_MODAL'}

    def modal(self, context, event, tweak):
        """Handle mouse interaction"""
        if event.type == 'MOUSEMOVE':
            # Get the active SDF item
            scene = context.scene
            if hasattr(scene, 'sdf_tree') and scene.sdf_tree.items:
                tree = scene.sdf_tree
                if 0 <= tree.active_index < len(tree.items):
                    item = tree.items[tree.active_index]

                    # Calculate movement delta
                    delta = tweak.delta
                    sensitivity = 0.01

                    # Apply movement based on gizmo axis
                    if hasattr(self, 'axis'):
                        if self.axis == 'X':
                            item.location[0] += delta[0] * sensitivity
                        elif self.axis == 'Y':
                            item.location[1] += delta[1] * sensitivity
                        elif self.axis == 'Z':
                            item.location[2] += delta[2] * sensitivity

                    # Update viewport
                    self._update_viewport()

        elif event.type in {'LEFTMOUSE', 'RET'}:
            return {'FINISHED'}
        elif event.type in {'RIGHTMOUSE', 'ESC'}:
            return {'CANCELLED'}

        return {'RUNNING_MODAL'}

    def _update_viewport(self):
        """Update the SDF viewport"""
        try:
            from .shaders import SDFRenderer
            SDFRenderer.refresh_shader()
        except:
            pass

class SDF_GT_RotateGizmo(Gizmo):
    """Rotation gizmo for SDF items"""
    bl_idname = "SDF_GT_rotate_gizmo"

    def setup(self):
        """Setup the gizmo"""
        # Use built-in shapes instead of custom shapes
        if hasattr(self, 'use_draw_modal'):
            self.use_draw_modal = True
        self.scale_basis = 1.0

    def draw(self, context):
        """Draw the gizmo"""
        # Use built-in drawing
        if hasattr(self, 'draw_preset'):
            self.draw_preset('RING_2D')
        else:
            # Fallback - don't draw anything complex
            pass

    def draw_select(self, context, select_id):
        """Draw for selection"""
        if hasattr(self, 'draw_preset'):
            self.draw_preset('RING_2D', select_id=select_id)
    
    def modal(self, context, event, tweak):
        """Handle rotation interaction"""
        if event.type == 'MOUSEMOVE':
            scene = context.scene
            if hasattr(scene, 'sdf_tree') and scene.sdf_tree.items:
                tree = scene.sdf_tree
                if 0 <= tree.active_index < len(tree.items):
                    item = tree.items[tree.active_index]
                    
                    # Calculate rotation delta
                    delta = tweak.delta
                    rotation_speed = 0.01
                    
                    # Apply rotation based on gizmo axis
                    if hasattr(self, 'axis'):
                        if self.axis == 'X':
                            item.rotation[0] += delta[1] * rotation_speed
                        elif self.axis == 'Y':
                            item.rotation[1] += delta[0] * rotation_speed
                        elif self.axis == 'Z':
                            item.rotation[2] += delta[0] * rotation_speed
                    
                    # Update viewport
                    try:
                        from .shaders import SDFRenderer
                        SDFRenderer.refresh_shader()
                    except:
                        pass
        
        return {'RUNNING_MODAL'}

class SDF_GT_ScaleGizmo(Gizmo):
    """Scale gizmo for SDF items"""
    bl_idname = "SDF_GT_scale_gizmo"

    def setup(self):
        """Setup the gizmo"""
        # Use built-in shapes
        if hasattr(self, 'use_draw_modal'):
            self.use_draw_modal = True
        self.scale_basis = 1.0

    def draw(self, context):
        """Draw the gizmo"""
        # Use built-in drawing
        if hasattr(self, 'draw_preset'):
            self.draw_preset('CUBE')
        else:
            pass

    def draw_select(self, context, select_id):
        """Draw for selection"""
        if hasattr(self, 'draw_preset'):
            self.draw_preset('CUBE', select_id=select_id)
    
    def modal(self, context, event, tweak):
        """Handle scale interaction"""
        if event.type == 'MOUSEMOVE':
            scene = context.scene
            if hasattr(scene, 'sdf_tree') and scene.sdf_tree.items:
                tree = scene.sdf_tree
                if 0 <= tree.active_index < len(tree.items):
                    item = tree.items[tree.active_index]
                    
                    # Calculate scale delta
                    delta = tweak.delta
                    scale_speed = 0.01
                    
                    # Apply uniform scaling
                    scale_factor = 1.0 + (delta[0] + delta[1]) * scale_speed
                    item.scale[0] *= scale_factor
                    item.scale[1] *= scale_factor
                    item.scale[2] *= scale_factor
                    
                    # Update viewport
                    try:
                        from .shaders import SDFRenderer
                        SDFRenderer.refresh_shader()
                    except:
                        pass
        
        return {'RUNNING_MODAL'}

class SDF_GGT_TransformGizmoGroup(GizmoGroup):
    """Gizmo group for SDF transform manipulation"""
    bl_idname = "SDF_GGT_transform_gizmo_group"
    bl_label = "SDF Transform Gizmos"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'WINDOW'
    bl_options = {'3D', 'PERSISTENT'}
    
    @classmethod
    def poll(cls, context):
        """Check if gizmos should be shown"""
        # Only show gizmos when SDF is active and an item is selected
        scene = context.scene
        if not hasattr(scene, 'sdf_tree'):
            return False
        
        tree = scene.sdf_tree
        if not tree.items or tree.active_index < 0 or tree.active_index >= len(tree.items):
            return False
        
        # Check if SDF viewport rendering is enabled
        if hasattr(scene, 'sdf') and hasattr(scene.sdf, 'sdf_show_in_viewport'):
            return scene.sdf.sdf_show_in_viewport
        
        return False
    
    def setup(self, context):
        """Setup the gizmo group"""
        # Translation gizmos (X, Y, Z axes)
        self.translate_x = self.gizmos.new(SDF_GT_TranslateGizmo.bl_idname)
        self.translate_x.axis = 'X'
        self.translate_x.color = (1.0, 0.0, 0.0)  # Red for X
        self.translate_x.alpha = 0.8
        
        self.translate_y = self.gizmos.new(SDF_GT_TranslateGizmo.bl_idname)
        self.translate_y.axis = 'Y'
        self.translate_y.color = (0.0, 1.0, 0.0)  # Green for Y
        self.translate_y.alpha = 0.8
        
        self.translate_z = self.gizmos.new(SDF_GT_TranslateGizmo.bl_idname)
        self.translate_z.axis = 'Z'
        self.translate_z.color = (0.0, 0.0, 1.0)  # Blue for Z
        self.translate_z.alpha = 0.8
        
        # Rotation gizmos
        self.rotate_x = self.gizmos.new(SDF_GT_RotateGizmo.bl_idname)
        self.rotate_x.axis = 'X'
        self.rotate_x.color = (1.0, 0.0, 0.0)
        self.rotate_x.alpha = 0.6
        
        self.rotate_y = self.gizmos.new(SDF_GT_RotateGizmo.bl_idname)
        self.rotate_y.axis = 'Y'
        self.rotate_y.color = (0.0, 1.0, 0.0)
        self.rotate_y.alpha = 0.6
        
        self.rotate_z = self.gizmos.new(SDF_GT_RotateGizmo.bl_idname)
        self.rotate_z.axis = 'Z'
        self.rotate_z.color = (0.0, 0.0, 1.0)
        self.rotate_z.alpha = 0.6
        
        # Scale gizmo
        self.scale_uniform = self.gizmos.new(SDF_GT_ScaleGizmo.bl_idname)
        self.scale_uniform.color = (1.0, 1.0, 1.0)
        self.scale_uniform.alpha = 0.7
    
    def refresh(self, context):
        """Refresh gizmo positions and visibility"""
        scene = context.scene
        if not hasattr(scene, 'sdf_tree'):
            return
        
        tree = scene.sdf_tree
        if not tree.items or tree.active_index < 0 or tree.active_index >= len(tree.items):
            return
        
        # Get the active item
        item = tree.items[tree.active_index]
        
        # Calculate world position
        world_location = item.get_world_location(tree)
        location = Vector(world_location)
        
        # Position all gizmos at the item's world location
        for gizmo in [self.translate_x, self.translate_y, self.translate_z,
                      self.rotate_x, self.rotate_y, self.rotate_z,
                      self.scale_uniform]:
            gizmo.matrix_basis = Matrix.Translation(location)
        
        # Set up axis orientations for translation gizmos
        self.translate_x.matrix_basis = Matrix.Translation(location) @ Matrix.Rotation(0, 4, 'Z')
        self.translate_y.matrix_basis = Matrix.Translation(location) @ Matrix.Rotation(1.5708, 4, 'Z')  # 90 degrees
        self.translate_z.matrix_basis = Matrix.Translation(location) @ Matrix.Rotation(-1.5708, 4, 'Y')  # 90 degrees
        
        # Set up rotation gizmo orientations
        self.rotate_x.matrix_basis = Matrix.Translation(location) @ Matrix.Rotation(1.5708, 4, 'Y')
        self.rotate_y.matrix_basis = Matrix.Translation(location) @ Matrix.Rotation(1.5708, 4, 'X')
        self.rotate_z.matrix_basis = Matrix.Translation(location)

# Register classes
classes = [
    SDF_GT_TranslateGizmo,
    SDF_GT_RotateGizmo,
    SDF_GT_ScaleGizmo,
    SDF_GGT_TransformGizmoGroup,
]

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in reversed(classes):
        try:
            bpy.utils.unregister_class(cls)
        except:
            pass
