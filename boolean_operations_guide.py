#!/usr/bin/env python3
"""
Complete guide and examples for using Boolean Operations in Arcane SDF.
Run this from within Blender's Python console to see examples.
"""

import bpy

def clear_tree():
    """Clear the SDF tree and start fresh"""
    scene = bpy.context.scene
    if hasattr(scene, 'sdf_tree'):
        tree = scene.sdf_tree
        tree.items.clear()
        tree.active_index = 0
        # Add default sphere
        tree.add_item('SPHERE', name="Default Sphere")
        tree.active_index = 0
        print("Tree cleared and reset with default sphere")

def example_union():
    """Example: Create a union of sphere and box"""
    print("\n=== UNION EXAMPLE ===")
    print("Creating a union of sphere and box...")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Add a sphere at origin
    bpy.ops.sdf.tree_add_sphere()
    sphere_item = tree.items[-1]
    sphere_item.name = "Base Sphere"
    sphere_item.radius = 1.0
    sphere_item.location = (0.0, 0.0, 0.0)
    
    # Add a box offset to the right
    bpy.ops.sdf.tree_add_box()
    box_item = tree.items[-1]
    box_item.name = "Side Box"
    box_item.size = (0.8, 0.8, 0.8)
    box_item.location = (1.2, 0.0, 0.0)
    
    # Create union operation (automatically uses last 2 items)
    bpy.ops.sdf.create_boolean_operation(operation_type='UNION')
    
    print("✅ Union created! You should see a combined shape.")
    print("💡 Tip: Adjust the 'Smooth Radius' property for smooth blending.")

def example_subtraction():
    """Example: Create a subtraction (sphere with box hole)"""
    print("\n=== SUBTRACTION EXAMPLE ===")
    print("Creating a sphere with a box-shaped hole...")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Add a large sphere as base
    bpy.ops.sdf.tree_add_sphere()
    sphere_item = tree.items[-1]
    sphere_item.name = "Base Sphere"
    sphere_item.radius = 1.5
    sphere_item.location = (0.0, 0.0, 0.0)
    
    # Add a smaller box to subtract
    bpy.ops.sdf.tree_add_box()
    box_item = tree.items[-1]
    box_item.name = "Hole Box"
    box_item.size = (0.8, 0.8, 0.8)
    box_item.location = (0.0, 0.0, 0.0)
    
    # Create subtraction operation
    bpy.ops.sdf.create_boolean_operation(operation_type='SUBTRACT')
    
    print("✅ Subtraction created! You should see a sphere with a box-shaped hole.")
    print("💡 Tip: The first child is the base, others are subtracted from it.")

def example_intersection():
    """Example: Create an intersection of sphere and box"""
    print("\n=== INTERSECTION EXAMPLE ===")
    print("Creating intersection of sphere and box...")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Add a sphere
    bpy.ops.sdf.tree_add_sphere()
    sphere_item = tree.items[-1]
    sphere_item.name = "Sphere"
    sphere_item.radius = 1.2
    sphere_item.location = (0.0, 0.0, 0.0)
    
    # Add a box
    bpy.ops.sdf.tree_add_box()
    box_item = tree.items[-1]
    box_item.name = "Box"
    box_item.size = (1.0, 1.0, 1.0)
    box_item.location = (0.0, 0.0, 0.0)
    
    # Create intersection operation
    bpy.ops.sdf.create_boolean_operation(operation_type='INTERSECT')
    
    print("✅ Intersection created! You should see only the overlapping part.")
    print("💡 Tip: Only the parts where both shapes overlap will be visible.")

def example_complex_hierarchy():
    """Example: Create a complex shape with multiple operations"""
    print("\n=== COMPLEX HIERARCHY EXAMPLE ===")
    print("Creating a complex shape with nested operations...")
    
    clear_tree()
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    # Step 1: Create base shape (union of two spheres)
    bpy.ops.sdf.tree_add_sphere()
    sphere1 = tree.items[-1]
    sphere1.name = "Left Sphere"
    sphere1.radius = 0.8
    sphere1.location = (-0.7, 0.0, 0.0)
    
    bpy.ops.sdf.tree_add_sphere()
    sphere2 = tree.items[-1]
    sphere2.name = "Right Sphere"
    sphere2.radius = 0.8
    sphere2.location = (0.7, 0.0, 0.0)
    
    # Union the two spheres
    bpy.ops.sdf.create_boolean_operation(operation_type='UNION')
    union_op = tree.items[-1]
    union_op.name = "Base Union"
    union_op.smooth_radius = 0.3  # Smooth blending
    
    # Step 2: Add a cylinder to subtract
    bpy.ops.sdf.tree_add_cylinder()
    cylinder = tree.items[-1]
    cylinder.name = "Hole Cylinder"
    cylinder.radius = 0.4
    cylinder.height = 2.0
    cylinder.location = (0.0, 0.0, 0.0)
    
    # Note: For this complex example, you'd need to manually set parent indices
    # This is a limitation of the current UI - in a full implementation,
    # you'd have better selection tools
    
    print("✅ Complex shape started! Check the tree structure.")
    print("💡 Tip: For complex hierarchies, you may need to manually adjust parent indices.")

def show_tree_structure():
    """Display the current tree structure"""
    print("\n=== CURRENT TREE STRUCTURE ===")
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        print("No SDF tree found!")
        return
    
    tree = scene.sdf_tree
    print(f"Tree has {len(tree.items)} items:")
    
    for i, item in enumerate(tree.items):
        indent = "  " * item.indent_level
        parent_info = f" (parent: {item.parent_index})" if item.parent_index >= 0 else " (root)"
        enabled_info = " [DISABLED]" if not item.is_enabled else ""
        print(f"{indent}{i}: {item.name} ({item.item_type}){parent_info}{enabled_info}")
        
        # Show some properties
        if item.item_type == 'SPHERE':
            print(f"{indent}   - Radius: {item.radius}, Location: {item.location[:]}")
        elif item.item_type == 'BOX':
            print(f"{indent}   - Size: {item.size[:]}, Location: {item.location[:]}")
        elif item.item_type in ['UNION', 'SUBTRACT', 'INTERSECT']:
            print(f"{indent}   - Smooth Radius: {item.smooth_radius}")

def test_boolean_operations():
    """Test all boolean operations"""
    print("🚀 Testing Boolean Operations in Arcane SDF")
    print("=" * 50)
    
    # Test each operation type
    examples = [
        ("Union", example_union),
        ("Subtraction", example_subtraction),
        ("Intersection", example_intersection),
    ]
    
    for name, example_func in examples:
        try:
            example_func()
            show_tree_structure()
            input(f"\nPress Enter to continue to next example...")
        except Exception as e:
            print(f"❌ Error in {name} example: {e}")
    
    # Show complex example
    try:
        example_complex_hierarchy()
        show_tree_structure()
    except Exception as e:
        print(f"❌ Error in complex example: {e}")
    
    print("\n🎉 Boolean operations testing complete!")
    print("\n📚 QUICK REFERENCE:")
    print("1. Add primitives using the primitive buttons")
    print("2. Use the smart boolean buttons (Union/Subtract/Intersect)")
    print("3. Adjust 'Smooth Radius' for smooth blending")
    print("4. Check the tree structure to understand hierarchy")
    print("5. Enable/disable items to see their effect")

if __name__ == "__main__":
    test_boolean_operations()
