"""
SDF Viewport Widgets using draw handlers.
Alternative approach to gizmos for transform manipulation.
"""

import bpy
import gpu
from gpu_extras.batch import batch_for_shader
from mathutils import Vector, Matrix
import bpy_extras.view3d_utils

# Global state for widget interaction
_widget_state = {
    'active': False,
    'dragging': False,
    'drag_axis': None,
    'drag_start_mouse': None,
    'drag_start_value': None,
    'draw_handler': None,
}

def get_active_sdf_item():
    """Get the currently active SDF item"""
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        return None
    
    tree = scene.sdf_tree
    if not tree.items or tree.active_index < 0 or tree.active_index >= len(tree.items):
        return None
    
    return tree.items[tree.active_index], tree

def world_to_screen(context, world_pos):
    """Convert world coordinates to screen coordinates"""
    region = context.region
    rv3d = context.region_data
    
    # Convert to screen coordinates
    screen_pos = bpy_extras.view3d_utils.location_3d_to_region_2d(
        region, rv3d, world_pos
    )
    return screen_pos



def draw_transform_widget():
    """Draw transform widget for the active SDF item"""
    item, tree = get_active_sdf_item()
    if not item:
        return

    try:
        # Get world location
        world_location = Vector(item.get_world_location(tree))

        # Set up GPU state
        gpu.state.blend_set('ALPHA')
        gpu.state.depth_test_set('LESS_EQUAL')
        gpu.state.line_width_set(3.0)

        # Draw simple coordinate axes
        arrow_length = 1.0

        # Create simple line geometry for axes
        shader = gpu.shader.from_builtin('3D_UNIFORM_COLOR')

        # X axis (red)
        x_verts = [world_location, world_location + Vector((arrow_length, 0, 0))]
        batch = batch_for_shader(shader, 'LINES', {"pos": x_verts})
        shader.bind()
        shader.uniform_float("color", (1.0, 0.0, 0.0, 0.8))
        batch.draw(shader)

        # Y axis (green)
        y_verts = [world_location, world_location + Vector((0, arrow_length, 0))]
        batch = batch_for_shader(shader, 'LINES', {"pos": y_verts})
        shader.bind()
        shader.uniform_float("color", (0.0, 1.0, 0.0, 0.8))
        batch.draw(shader)

        # Z axis (blue)
        z_verts = [world_location, world_location + Vector((0, 0, arrow_length))]
        batch = batch_for_shader(shader, 'LINES', {"pos": z_verts})
        shader.bind()
        shader.uniform_float("color", (0.0, 0.0, 1.0, 0.8))
        batch.draw(shader)

        # Draw center point
        center_verts = [world_location]
        gpu.state.point_size_set(8.0)
        batch = batch_for_shader(shader, 'POINTS', {"pos": center_verts})
        shader.bind()
        shader.uniform_float("color", (1.0, 1.0, 1.0, 1.0))
        batch.draw(shader)

        # Restore GPU state
        gpu.state.blend_set('NONE')
        gpu.state.depth_test_set('NONE')
        gpu.state.line_width_set(1.0)
        gpu.state.point_size_set(1.0)

    except Exception as e:
        print(f"Widget draw error: {e}")

def viewport_draw_handler():
    """Main draw handler for viewport widgets"""
    try:
        # Only draw if SDF viewport is enabled
        scene = bpy.context.scene
        if not hasattr(scene, 'sdf') or not scene.sdf.sdf_show_in_viewport:
            return
        
        # Only draw if we have an active item
        item, tree = get_active_sdf_item()
        if not item:
            return
        
        # Draw the transform widget
        draw_transform_widget()
        
    except Exception as e:
        print(f"SDF Widget Draw Error: {e}")

def handle_mouse_move(context, event):
    """Handle mouse movement for widget interaction"""
    if not _widget_state['dragging']:
        return False
    
    # Calculate mouse delta
    mouse_pos = Vector((event.mouse_region_x, event.mouse_region_y))
    mouse_delta = mouse_pos - _widget_state['drag_start_mouse']
    
    # Get active item
    item, tree = get_active_sdf_item()
    if not item:
        return False
    
    # Apply transform based on drag axis
    sensitivity = 0.01
    axis = _widget_state['drag_axis']
    
    if axis == 'X':
        item.location[0] = _widget_state['drag_start_value'] + mouse_delta.x * sensitivity
    elif axis == 'Y':
        item.location[1] = _widget_state['drag_start_value'] + mouse_delta.y * sensitivity
    elif axis == 'Z':
        item.location[2] = _widget_state['drag_start_value'] + mouse_delta.y * sensitivity
    
    # Update viewport
    try:
        from .shaders import SDFRenderer
        SDFRenderer.refresh_shader()
    except:
        pass
    
    return True

def handle_mouse_press(context, event):
    """Handle mouse press for widget interaction"""
    if event.type != 'LEFTMOUSE' or event.value != 'PRESS':
        return False
    
    # Check if we're clicking on a widget axis
    # (This is a simplified version - a full implementation would do proper hit testing)
    
    item, tree = get_active_sdf_item()
    if not item:
        return False
    
    # For now, just start dragging on X axis as an example
    # A full implementation would determine which axis was clicked
    _widget_state['dragging'] = True
    _widget_state['drag_axis'] = 'X'  # This should be determined by hit testing
    _widget_state['drag_start_mouse'] = Vector((event.mouse_region_x, event.mouse_region_y))
    _widget_state['drag_start_value'] = item.location[0]
    
    return True

def handle_mouse_release(context, event):
    """Handle mouse release to end widget interaction"""
    if event.type != 'LEFTMOUSE' or event.value != 'RELEASE':
        return False
    
    if _widget_state['dragging']:
        _widget_state['dragging'] = False
        _widget_state['drag_axis'] = None
        _widget_state['drag_start_mouse'] = None
        _widget_state['drag_start_value'] = None
        return True
    
    return False

class SDF_OT_ToggleViewportWidgets(bpy.types.Operator):
    """Toggle SDF viewport transform widgets"""
    bl_idname = "sdf.toggle_viewport_widgets"
    bl_label = "Toggle Viewport Widgets"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        if _widget_state['active']:
            disable_viewport_widgets()
            self.report({'INFO'}, "SDF viewport widgets disabled")
        else:
            enable_viewport_widgets()
            self.report({'INFO'}, "SDF viewport widgets enabled")
        
        return {'FINISHED'}

def enable_viewport_widgets():
    """Enable viewport widgets"""
    if _widget_state['active']:
        return
    
    # Add draw handler
    _widget_state['draw_handler'] = bpy.types.SpaceView3D.draw_handler_add(
        viewport_draw_handler, (), 'WINDOW', 'POST_VIEW'
    )
    
    _widget_state['active'] = True
    print("SDF viewport widgets enabled")

def disable_viewport_widgets():
    """Disable viewport widgets"""
    if not _widget_state['active']:
        return
    
    # Remove draw handler
    if _widget_state['draw_handler']:
        bpy.types.SpaceView3D.draw_handler_remove(
            _widget_state['draw_handler'], 'WINDOW'
        )
        _widget_state['draw_handler'] = None
    
    _widget_state['active'] = False
    _widget_state['dragging'] = False
    print("SDF viewport widgets disabled")

# Modal operator for handling mouse interaction
class SDF_OT_ViewportWidgetModal(bpy.types.Operator):
    """Modal operator for viewport widget interaction"""
    bl_idname = "sdf.viewport_widget_modal"
    bl_label = "SDF Viewport Widget Interaction"
    
    def modal(self, context, event):
        # Handle mouse events
        if handle_mouse_press(context, event):
            return {'RUNNING_MODAL'}
        
        if handle_mouse_move(context, event):
            return {'RUNNING_MODAL'}
        
        if handle_mouse_release(context, event):
            return {'RUNNING_MODAL'}
        
        # Exit on ESC
        if event.type == 'ESC':
            return {'CANCELLED'}
        
        return {'PASS_THROUGH'}
    
    def invoke(self, context, event):
        context.window_manager.modal_handler_add(self)
        return {'RUNNING_MODAL'}

# Register classes
classes = [
    SDF_OT_ToggleViewportWidgets,
    SDF_OT_ViewportWidgetModal,
]

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    # Disable widgets first
    disable_viewport_widgets()
    
    for cls in reversed(classes):
        try:
            bpy.utils.unregister_class(cls)
        except:
            pass
