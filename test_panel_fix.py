"""
Quick test script to verify the panel error is fixed.
Run this in Blender's Text Editor to test the nesting functionality.
"""

import bpy

def test_panel_access():
    """Test that the panel can access the tree variable without errors"""
    print("🧪 Testing panel variable access...")
    
    # Ensure we have a scene
    scene = bpy.context.scene
    
    # Check if sdf_tree exists
    if not hasattr(scene, 'sdf_tree'):
        print("❌ SDF tree not found. Make sure the addon is enabled.")
        return False
    
    tree = scene.sdf_tree
    print(f"✅ SDF tree found with {len(tree.items)} items")
    
    # Test basic tree operations
    try:
        # Clear tree
        tree.items.clear()
        tree.active_index = 0
        
        # Add a group
        bpy.ops.sdf.tree_add_group()
        print("✅ Group added successfully")
        
        # Add a child
        tree.active_index = 0
        bpy.ops.sdf.tree_add_as_child(item_type='SPHERE')
        print("✅ Child added successfully")
        
        # Check hierarchy
        children = tree.get_children(0)
        if len(children) == 1:
            print("✅ Hierarchy working correctly")
        else:
            print(f"❌ Expected 1 child, got {len(children)}")
            return False
            
        # Test GLSL generation
        glsl = tree.generate_glsl()
        if "return" in glsl:
            print("✅ GLSL generation working")
        else:
            print("❌ GLSL generation failed")
            return False
            
        print("🎉 All tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def test_ui_access():
    """Test that UI elements can be accessed without variable errors"""
    print("\n🖥️  Testing UI access...")
    
    try:
        # Try to access the scene and tree like the panel does
        scene = bpy.context.scene
        
        if hasattr(scene, 'sdf_tree'):
            tree = scene.sdf_tree
            
            # Test the conditions that were causing the error
            if tree.items and 0 <= tree.active_index < len(tree.items):
                print("✅ Tree access conditions working")
            else:
                print("ℹ️  Tree is empty or no active item (this is normal)")
            
            # Test children access
            if tree.active_index >= 0 and tree.active_index < len(tree.items):
                children = tree.get_children(tree.active_index)
                print(f"✅ Children access working: {len(children)} children")
            else:
                print("ℹ️  No active item to check children for")
                
            return True
        else:
            print("❌ SDF tree not available")
            return False
            
    except Exception as e:
        print(f"❌ UI access test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Panel Fix Tests")
    
    success1 = test_panel_access()
    success2 = test_ui_access()
    
    if success1 and success2:
        print("\n✅ All tests passed! The panel error should be fixed.")
    else:
        print("\n❌ Some tests failed. Check the implementation.")
