"""
Simple SDF Viewport Widgets using draw handlers.
A more reliable approach than complex gizmos.
"""

import bpy
import gpu
from gpu_extras.batch import batch_for_shader
from mathutils import Vector
import bmesh

# Global state for widget interaction
_widget_state = {
    'enabled': False,
    'draw_handler': None,
    'mouse_handler': None,
    'dragging': False,
    'drag_axis': None,
    'drag_start_mouse': None,
    'drag_start_location': None,
    'last_active_item': None,  # Track active item changes
    'last_item_location': None,  # Track item location changes for sync
}

def get_active_sdf_item():
    """Get the currently active SDF item and tree"""
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        return None, None
    
    tree = scene.sdf_tree
    if not tree.items or tree.active_index < 0 or tree.active_index >= len(tree.items):
        return None, None
    
    return tree.items[tree.active_index], tree

def draw_simple_widget():
    """Draw simple transform widget for the active SDF item"""
    item, tree = get_active_sdf_item()
    if not item:
        return

    try:
        # Always get the current world location (this ensures gizmo follows the item)
        world_location = Vector(item.get_world_location(tree))

        # The gizmo should always draw at the item's current world location
        # No caching, no complex calculations - just use the current location

        # Set up GPU state
        gpu.state.blend_set('ALPHA')
        gpu.state.depth_test_set('LESS_EQUAL')
        gpu.state.line_width_set(6.0)  # Thicker lines for easier clicking

        # Create shader (use the correct shader name for current Blender version)
        shader = gpu.shader.from_builtin('UNIFORM_COLOR')
        
        # Draw coordinate axes
        axis_length = 1.5
        
        # X axis (red)
        x_verts = [
            world_location,
            world_location + Vector((axis_length, 0, 0))
        ]
        batch = batch_for_shader(shader, 'LINES', {"pos": x_verts})
        shader.bind()
        shader.uniform_float("color", (1.0, 0.2, 0.2, 0.9))
        batch.draw(shader)
        
        # Y axis (green)
        y_verts = [
            world_location,
            world_location + Vector((0, axis_length, 0))
        ]
        batch = batch_for_shader(shader, 'LINES', {"pos": y_verts})
        shader.bind()
        shader.uniform_float("color", (0.2, 1.0, 0.2, 0.9))
        batch.draw(shader)
        
        # Z axis (blue)
        z_verts = [
            world_location,
            world_location + Vector((0, 0, axis_length))
        ]
        batch = batch_for_shader(shader, 'LINES', {"pos": z_verts})
        shader.bind()
        shader.uniform_float("color", (0.2, 0.2, 1.0, 0.9))
        batch.draw(shader)

        # Draw axis end indicators (crosshairs for better click targets)
        end_indicator_size = 0.08

        # X axis end indicator (red crosshair)
        x_end_pos = world_location + Vector((axis_length, 0, 0))
        x_indicator_verts = [
            x_end_pos + Vector((0, -end_indicator_size, 0)),
            x_end_pos + Vector((0, end_indicator_size, 0)),
            x_end_pos + Vector((0, 0, -end_indicator_size)),
            x_end_pos + Vector((0, 0, end_indicator_size)),
        ]
        batch = batch_for_shader(shader, 'LINES', {"pos": x_indicator_verts})
        shader.bind()
        shader.uniform_float("color", (1.0, 0.2, 0.2, 1.0))
        batch.draw(shader)

        # Y axis end indicator (green crosshair)
        y_end_pos = world_location + Vector((0, axis_length, 0))
        y_indicator_verts = [
            y_end_pos + Vector((-end_indicator_size, 0, 0)),
            y_end_pos + Vector((end_indicator_size, 0, 0)),
            y_end_pos + Vector((0, 0, -end_indicator_size)),
            y_end_pos + Vector((0, 0, end_indicator_size)),
        ]
        batch = batch_for_shader(shader, 'LINES', {"pos": y_indicator_verts})
        shader.bind()
        shader.uniform_float("color", (0.2, 1.0, 0.2, 1.0))
        batch.draw(shader)

        # Z axis end indicator (blue crosshair)
        z_end_pos = world_location + Vector((0, 0, axis_length))
        z_indicator_verts = [
            z_end_pos + Vector((-end_indicator_size, 0, 0)),
            z_end_pos + Vector((end_indicator_size, 0, 0)),
            z_end_pos + Vector((0, -end_indicator_size, 0)),
            z_end_pos + Vector((0, end_indicator_size, 0)),
        ]
        batch = batch_for_shader(shader, 'LINES', {"pos": z_indicator_verts})
        shader.bind()
        shader.uniform_float("color", (0.2, 0.2, 1.0, 1.0))
        batch.draw(shader)

        # Draw axis end points (larger for easier clicking)
        axis_end_radius = 0.15

        # X axis end point (red sphere)
        x_end = world_location + Vector((axis_length, 0, 0))
        x_end_verts = []

        # Y axis end point (green sphere)
        y_end = world_location + Vector((0, axis_length, 0))
        y_end_verts = []

        # Z axis end point (blue sphere)
        z_end = world_location + Vector((0, 0, axis_length))
        z_end_verts = []

        # Draw center sphere (as multiple lines to form a wireframe sphere)
        sphere_radius = 0.1
        sphere_verts = []

        # Create simple wireframe sphere with circles
        import math
        segments = 16
        
        # XY circle
        for i in range(segments):
            angle1 = (i / segments) * 2 * math.pi
            angle2 = ((i + 1) / segments) * 2 * math.pi
            x1 = world_location.x + math.cos(angle1) * sphere_radius
            y1 = world_location.y + math.sin(angle1) * sphere_radius
            x2 = world_location.x + math.cos(angle2) * sphere_radius
            y2 = world_location.y + math.sin(angle2) * sphere_radius
            sphere_verts.extend([
                Vector((x1, y1, world_location.z)),
                Vector((x2, y2, world_location.z))
            ])
        
        # XZ circle
        for i in range(segments):
            angle1 = (i / segments) * 2 * math.pi
            angle2 = ((i + 1) / segments) * 2 * math.pi
            x1 = world_location.x + math.cos(angle1) * sphere_radius
            z1 = world_location.z + math.sin(angle1) * sphere_radius
            x2 = world_location.x + math.cos(angle2) * sphere_radius
            z2 = world_location.z + math.sin(angle2) * sphere_radius
            sphere_verts.extend([
                Vector((x1, world_location.y, z1)),
                Vector((x2, world_location.y, z2))
            ])
        
        # YZ circle
        for i in range(segments):
            angle1 = (i / segments) * 2 * math.pi
            angle2 = ((i + 1) / segments) * 2 * math.pi
            y1 = world_location.y + math.cos(angle1) * sphere_radius
            z1 = world_location.z + math.sin(angle1) * sphere_radius
            y2 = world_location.y + math.cos(angle2) * sphere_radius
            z2 = world_location.z + math.sin(angle2) * sphere_radius
            sphere_verts.extend([
                Vector((world_location.x, y1, z1)),
                Vector((world_location.x, y2, z2))
            ])
        
        # Draw sphere wireframe
        if sphere_verts:
            batch = batch_for_shader(shader, 'LINES', {"pos": sphere_verts})
            shader.bind()
            shader.uniform_float("color", (1.0, 1.0, 1.0, 0.8))
            batch.draw(shader)
        
        # Draw item name
        # (Text drawing would require more complex implementation)

        # Restore GPU state
        gpu.state.blend_set('NONE')
        gpu.state.depth_test_set('NONE')
        gpu.state.line_width_set(1.0)

    except Exception as e:
        print(f"Widget draw error: {e}")

def viewport_draw_handler():
    """Main draw handler for viewport widgets"""
    try:
        # Only draw if we have an active item
        item, tree = get_active_sdf_item()
        if not item:
            _widget_state['last_active_item'] = None
            return

        # Check if we're in a valid drawing context
        context = bpy.context
        if not context or not context.area or context.area.type != 'VIEW_3D':
            return

        # Simple approach: just draw the gizmo at the current item location
        # The drawing function will get the current location each time

        # Draw the simple widget
        draw_simple_widget()

    except Exception as e:
        print(f"SDF Widget Draw Handler Error: {e}")
        # Don't print full traceback in draw handler as it can spam console

class SDF_OT_ToggleSimpleWidgets(bpy.types.Operator):
    """Toggle simple SDF viewport transform widgets"""
    bl_idname = "sdf.toggle_simple_widgets"
    bl_label = "Toggle Simple Widgets"
    bl_options = {'REGISTER'}

    def execute(self, context):
        print(f"Toggle widgets called - current state: {_widget_state['enabled']}")

        if _widget_state['enabled']:
            disable_simple_widgets()
            self.report({'INFO'}, "SDF simple widgets disabled")
        else:
            enable_simple_widgets()
            self.report({'INFO'}, "SDF simple widgets enabled")

        # Debug: Check if we have an active item
        item, tree = get_active_sdf_item()
        if item:
            print(f"Active SDF item: {item.name} at {item.get_world_location(tree)}")
        else:
            print("No active SDF item found")

        return {'FINISHED'}

class SDF_OT_DebugWidgets(bpy.types.Operator):
    """Debug widget system (safe version)"""
    bl_idname = "sdf.debug_widgets"
    bl_label = "Debug Widgets"
    bl_options = {'REGISTER'}

    def execute(self, context):
        print("=== SDF Widget Debug (Safe) ===")
        print(f"Widget enabled: {_widget_state['enabled']}")
        print(f"Draw handler registered: {_widget_state['draw_handler'] is not None}")

        # Check active item
        item, tree = get_active_sdf_item()
        if item:
            print(f"✅ Active item: {item.name} ({item.item_type})")
            print(f"   Local: {tuple(item.location)}")
            print(f"   World: {item.get_world_location(tree)}")
        else:
            print("❌ No active SDF item")

        # Check scene
        scene = context.scene
        if hasattr(scene, 'sdf_tree'):
            tree = scene.sdf_tree
            print(f"✅ Tree: {len(tree.items)} items, active index: {tree.active_index}")
        else:
            print("❌ No SDF tree found")

        # Check viewport
        if context.area and context.area.type == 'VIEW_3D':
            print("✅ In 3D viewport")
        else:
            print("⚠️  Not in 3D viewport")

        print("=== Debug Complete ===")
        self.report({'INFO'}, "Debug info printed to console")

        return {'FINISHED'}

class SDF_OT_GizmoInteraction(bpy.types.Operator):
    """Handle gizmo mouse interaction"""
    bl_idname = "sdf.gizmo_interaction"
    bl_label = "SDF Gizmo Interaction"
    bl_options = {'REGISTER', 'UNDO'}

    def modal(self, context, event):
        if event.type == 'MOUSEMOVE' and _widget_state['dragging']:
            # Get active item
            item, tree = get_active_sdf_item()
            if not item:
                return {'CANCELLED'}

            # Calculate mouse movement
            mouse_pos = Vector((event.mouse_region_x, event.mouse_region_y))
            mouse_delta = mouse_pos - _widget_state['drag_start_mouse']

            # Apply movement with very simple calculation
            axis = _widget_state['drag_axis']
            sensitivity = 0.002  # Very small sensitivity

            # Calculate new position based on original position + delta
            if axis == 'X':
                item.location[0] = _widget_state['drag_start_location'][0] + mouse_delta.x * sensitivity
            elif axis == 'Y':
                item.location[1] = _widget_state['drag_start_location'][1] + mouse_delta.x * sensitivity
            elif axis == 'Z':
                item.location[2] = _widget_state['drag_start_location'][2] + mouse_delta.y * sensitivity

            # Update SDF rendering
            try:
                from .shaders import SDFRenderer
                SDFRenderer.refresh_shader()
            except:
                pass

        elif event.type == 'LEFTMOUSE' and event.value == 'PRESS':
            # Check if clicking on a gizmo axis
            axis = self.detect_axis_click(context, event)
            if axis:
                # Start dragging
                item, tree = get_active_sdf_item()
                if item:
                    _widget_state['dragging'] = True
                    _widget_state['drag_axis'] = axis
                    _widget_state['drag_start_mouse'] = Vector((event.mouse_region_x, event.mouse_region_y))
                    _widget_state['drag_start_location'] = list(item.location)
                    return {'RUNNING_MODAL'}

        elif event.type == 'LEFTMOUSE' and event.value == 'RELEASE':
            if _widget_state['dragging']:
                _widget_state['dragging'] = False
                _widget_state['drag_axis'] = None
                _widget_state['drag_start_mouse'] = None
                _widget_state['drag_start_location'] = None

                # Force viewport redraw to ensure gizmo is at correct position
                context.area.tag_redraw()
                return {'FINISHED'}

        elif event.type in {'RIGHTMOUSE', 'ESC'}:
            if _widget_state['dragging']:
                # Cancel drag - restore original location
                item, tree = get_active_sdf_item()
                if item and _widget_state['drag_start_location']:
                    item.location = _widget_state['drag_start_location']
                    try:
                        from .shaders import SDFRenderer
                        SDFRenderer.refresh_shader()
                    except:
                        pass

                _widget_state['dragging'] = False
                _widget_state['drag_axis'] = None
                _widget_state['drag_start_mouse'] = None
                _widget_state['drag_start_location'] = None
                return {'CANCELLED'}

        return {'PASS_THROUGH'}

    def detect_axis_click(self, context, event):
        """Detect which axis line was clicked"""
        item, tree = get_active_sdf_item()
        if not item:
            return None

        try:
            import bpy_extras.view3d_utils
            region = context.region
            rv3d = context.region_data

            if not region or not rv3d:
                return None

            # Get world locations
            world_location = Vector(item.get_world_location(tree))
            axis_length = 1.5

            # Calculate axis end points in world space
            x_end = world_location + Vector((axis_length, 0, 0))
            y_end = world_location + Vector((0, axis_length, 0))
            z_end = world_location + Vector((0, 0, axis_length))

            # Convert to screen coordinates
            center_screen = bpy_extras.view3d_utils.location_3d_to_region_2d(region, rv3d, world_location)
            x_end_screen = bpy_extras.view3d_utils.location_3d_to_region_2d(region, rv3d, x_end)
            y_end_screen = bpy_extras.view3d_utils.location_3d_to_region_2d(region, rv3d, y_end)
            z_end_screen = bpy_extras.view3d_utils.location_3d_to_region_2d(region, rv3d, z_end)

            if not all([center_screen, x_end_screen, y_end_screen, z_end_screen]):
                return None

            mouse_pos = Vector((event.mouse_region_x, event.mouse_region_y))

            # Check distance to each axis line
            click_threshold = 15  # pixels

            # Debug: Print distances for troubleshooting
            # Uncomment next lines for debugging
            # print(f"Mouse: {mouse_pos}, Center: {center_screen}")

            # X axis (red line)
            x_distance = self.point_to_line_distance_2d(mouse_pos, center_screen, x_end_screen)
            if x_distance < click_threshold:
                # Make sure click is along the line, not beyond the endpoints
                if self.point_on_line_segment_2d(mouse_pos, center_screen, x_end_screen):
                    print(f"X-axis clicked! Distance: {x_distance:.1f}px")
                    return 'X'

            # Y axis (green line)
            y_distance = self.point_to_line_distance_2d(mouse_pos, center_screen, y_end_screen)
            if y_distance < click_threshold:
                if self.point_on_line_segment_2d(mouse_pos, center_screen, y_end_screen):
                    print(f"Y-axis clicked! Distance: {y_distance:.1f}px")
                    return 'Y'

            # Z axis (blue line)
            z_distance = self.point_to_line_distance_2d(mouse_pos, center_screen, z_end_screen)
            if z_distance < click_threshold:
                if self.point_on_line_segment_2d(mouse_pos, center_screen, z_end_screen):
                    print(f"Z-axis clicked! Distance: {z_distance:.1f}px")
                    return 'Z'

            # Debug: Print all distances if no axis was clicked
            # Uncomment for debugging
            # print(f"No axis clicked. Distances - X: {x_distance:.1f}, Y: {y_distance:.1f}, Z: {z_distance:.1f}")

        except Exception as e:
            print(f"Axis detection error: {e}")

        return None

    def point_to_line_distance_2d(self, point, line_start, line_end):
        """Calculate distance from point to line segment in 2D"""
        # Convert to Vector if needed
        point = Vector(point)
        line_start = Vector(line_start)
        line_end = Vector(line_end)

        # Line vector
        line_vec = line_end - line_start
        line_length_sq = line_vec.length_squared

        if line_length_sq == 0:
            return (point - line_start).length

        # Project point onto line
        t = max(0, min(1, (point - line_start).dot(line_vec) / line_length_sq))
        projection = line_start + t * line_vec

        return (point - projection).length

    def point_on_line_segment_2d(self, point, line_start, line_end):
        """Check if point is within the line segment bounds (with some tolerance)"""
        point = Vector(point)
        line_start = Vector(line_start)
        line_end = Vector(line_end)

        # Check if point is within the bounding box of the line segment (with tolerance)
        tolerance = 20  # pixels

        min_x = min(line_start.x, line_end.x) - tolerance
        max_x = max(line_start.x, line_end.x) + tolerance
        min_y = min(line_start.y, line_end.y) - tolerance
        max_y = max(line_start.y, line_end.y) + tolerance

        return (min_x <= point.x <= max_x) and (min_y <= point.y <= max_y)

    def invoke(self, context, event):
        context.window_manager.modal_handler_add(self)
        return {'RUNNING_MODAL'}

class SDF_OT_MoveItemInteractive(bpy.types.Operator):
    """Interactively move SDF item with mouse"""
    bl_idname = "sdf.move_item_interactive"
    bl_label = "Move Item Interactive"
    bl_options = {'REGISTER', 'UNDO'}
    
    axis: bpy.props.EnumProperty(
        items=[
            ('X', "X Axis", "Move along X axis"),
            ('Y', "Y Axis", "Move along Y axis"),
            ('Z', "Z Axis", "Move along Z axis"),
        ],
        default='X'
    )
    
    def modal(self, context, event):
        if event.type == 'MOUSEMOVE':
            # Get mouse delta
            delta_x = event.mouse_x - self.initial_mouse_x
            delta_y = event.mouse_y - self.initial_mouse_y
            
            # Calculate movement based on axis
            sensitivity = 0.01
            
            if self.axis == 'X':
                movement = delta_x * sensitivity
                self.item.location[0] = self.initial_location[0] + movement
            elif self.axis == 'Y':
                movement = delta_x * sensitivity
                self.item.location[1] = self.initial_location[1] + movement
            elif self.axis == 'Z':
                movement = delta_y * sensitivity
                self.item.location[2] = self.initial_location[2] + movement
            
            # Update viewport and gizmo
            try:
                from .shaders import SDFRenderer
                SDFRenderer.refresh_shader()
            except:
                pass

            # Force redraw of all 3D viewports to update gizmo position
            for area in bpy.context.screen.areas:
                if area.type == 'VIEW_3D':
                    area.tag_redraw()
        
        elif event.type == 'LEFTMOUSE':
            return {'FINISHED'}
        elif event.type in {'RIGHTMOUSE', 'ESC'}:
            # Restore original location
            self.item.location = self.initial_location
            try:
                from .shaders import SDFRenderer
                SDFRenderer.refresh_shader()
            except:
                pass
            return {'CANCELLED'}
        
        return {'RUNNING_MODAL'}
    
    def invoke(self, context, event):
        # Get active item
        item, tree = get_active_sdf_item()
        if not item:
            self.report({'ERROR'}, "No active SDF item")
            return {'CANCELLED'}
        
        self.item = item
        self.initial_mouse_x = event.mouse_x
        self.initial_mouse_y = event.mouse_y
        self.initial_location = list(item.location)
        
        context.window_manager.modal_handler_add(self)
        return {'RUNNING_MODAL'}

def enable_simple_widgets():
    """Enable simple viewport widgets"""
    if _widget_state['enabled']:
        print("SDF simple widgets already enabled")
        return

    try:
        # Add draw handler
        _widget_state['draw_handler'] = bpy.types.SpaceView3D.draw_handler_add(
            viewport_draw_handler, (), 'WINDOW', 'POST_VIEW'
        )

        # Start gizmo interaction modal operator
        bpy.ops.sdf.gizmo_interaction('INVOKE_DEFAULT')

        _widget_state['enabled'] = True
        print(f"✅ SDF simple widgets enabled - handler: {_widget_state['draw_handler']}")

        # Force a viewport redraw
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()

    except Exception as e:
        print(f"❌ Failed to enable SDF simple widgets: {e}")
        import traceback
        traceback.print_exc()

def disable_simple_widgets():
    """Disable simple viewport widgets"""
    if not _widget_state['enabled']:
        print("SDF simple widgets already disabled")
        return

    try:
        # Remove draw handler
        if _widget_state['draw_handler']:
            bpy.types.SpaceView3D.draw_handler_remove(
                _widget_state['draw_handler'], 'WINDOW'
            )
            print(f"✅ Removed draw handler: {_widget_state['draw_handler']}")
            _widget_state['draw_handler'] = None

        _widget_state['enabled'] = False
        print("✅ SDF simple widgets disabled")

        # Force a viewport redraw
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()

    except Exception as e:
        print(f"❌ Failed to disable SDF simple widgets: {e}")
        import traceback
        traceback.print_exc()

# Register classes
classes = [
    SDF_OT_ToggleSimpleWidgets,
    SDF_OT_DebugWidgets,
    SDF_OT_GizmoInteraction,
    SDF_OT_MoveItemInteractive,
]

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    # Disable widgets first
    disable_simple_widgets()
    
    for cls in reversed(classes):
        try:
            bpy.utils.unregister_class(cls)
        except:
            pass
