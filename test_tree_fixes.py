#!/usr/bin/env python3
"""
Test script to verify SDF tree fixes work correctly.
Run this from within Blender's Python console.
"""

import bpy

def test_tree_initialization():
    """Test that the tree initializes correctly with a default sphere"""
    print("=== Testing Tree Initialization ===")
    
    scene = bpy.context.scene
    
    # Check if tree property exists
    if not hasattr(scene, 'sdf_tree'):
        print("ERROR: sdf_tree property not found!")
        return False
    
    tree = scene.sdf_tree
    print(f"Tree has {len(tree.items)} items")
    
    # If tree is empty, it should auto-initialize
    if not tree.items:
        print("Tree is empty, calling ensure_default_item()")
        tree.ensure_default_item()
    
    print(f"After initialization: {len(tree.items)} items")
    
    if tree.items:
        for i, item in enumerate(tree.items):
            print(f"  Item {i}: {item.name} ({item.item_type}) - enabled: {item.is_enabled}")
    
    return len(tree.items) > 0

def test_add_primitives():
    """Test adding primitives to the tree"""
    print("\n=== Testing Add Primitives ===")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    initial_count = len(tree.items)
    print(f"Initial item count: {initial_count}")
    
    # Add a box
    print("Adding box...")
    bpy.ops.sdf.tree_add_box()
    
    print(f"After adding box: {len(tree.items)} items")
    
    # Add a cylinder
    print("Adding cylinder...")
    bpy.ops.sdf.tree_add_cylinder()
    
    print(f"After adding cylinder: {len(tree.items)} items")
    
    # List all items
    for i, item in enumerate(tree.items):
        print(f"  Item {i}: {item.name} ({item.item_type}) - enabled: {item.is_enabled}")
    
    return len(tree.items) > initial_count

def test_glsl_generation():
    """Test GLSL code generation"""
    print("\n=== Testing GLSL Generation ===")
    
    scene = bpy.context.scene
    tree = scene.sdf_tree
    
    try:
        glsl_code = tree.generate_glsl()
        print("Generated GLSL:")
        print(glsl_code)
        
        # Should not be empty and should not be the old default
        is_valid = glsl_code and "return 1000.0;" not in glsl_code
        print(f"GLSL generation successful: {is_valid}")
        return is_valid
        
    except Exception as e:
        print(f"GLSL generation failed: {e}")
        return False

def test_shader_update():
    """Test shader update"""
    print("\n=== Testing Shader Update ===")
    
    try:
        from .shaders import SDFRenderer
        SDFRenderer.refresh_shader()
        print("Shader refresh successful")
        return True
    except Exception as e:
        print(f"Shader refresh failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("Starting SDF Tree Fix Tests...")
    
    tests = [
        ("Tree Initialization", test_tree_initialization),
        ("Add Primitives", test_add_primitives),
        ("GLSL Generation", test_glsl_generation),
        ("Shader Update", test_shader_update),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{test_name}: {'PASS' if result else 'FAIL'}")
        except Exception as e:
            print(f"{test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    print("\n=== Test Summary ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"Passed: {passed}/{total}")
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name}: {status}")

if __name__ == "__main__":
    run_all_tests()
